{"name": "@askinfosec/api", "version": "0.0.1", "description": "", "author": "", "private": true, "scripts": {"clean": "rm -rf dist tsconfig.tsbuildinfo", "dev": "node --env-file=.env --watch -r ts-node/register -r tsconfig-paths/register src/main.ts", "build": "pnpm --filter @askinfosec/shared build && pnpm --filter @askinfosec/database-drizzle build && pnpm clean && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "node --env-file=.env --watch -r ts-node/register -r tsconfig-paths/register src/main.ts", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@askinfosec/database": "workspace:*", "@askinfosec/database-drizzle": "workspace:*", "@askinfosec/shared": "workspace:*", "@askinfosec/types": "workspace:*", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^8.0.7", "@nestjs/throttler": "^6.4.0", "better-auth": "^1.3.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "motion": "^12.23.12", "reflect-metadata": "^0.2.2", "resend": "^6.0.1", "rxjs": "^7.8.1", "stripe": "^18.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^30.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}