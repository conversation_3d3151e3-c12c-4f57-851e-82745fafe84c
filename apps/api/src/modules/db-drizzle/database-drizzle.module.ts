import { <PERSON><PERSON><PERSON>, <PERSON>, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import {
  ConnectionManager,
  createConnectionManager,
  OrganizationRepository,
  UserRepository,
  OrganizationModuleRepository,
  MemberRepository,
  RoleRepository,
  PermissionRepository,
  InviteRepository,
  MemberActivityRepository,
  ProductRepository,
  EmailAuthCodeRepository,
  SubscriptionRepository,
  SubscriptionTierRepository,
  SubscriptionPlanRepository,
  PriceRepository,
  FileRepository,
  type DrizzleDB,
} from '@askinfosec/database-drizzle';

// Provider tokens
export const DRIZZLE_CONNECTION_MANAGER = 'DRIZZLE_CONNECTION_MANAGER';
export const DRIZZLE_DB_CONTEXT = 'DRIZZLE_DB_CONTEXT';

// Interface for the database context
export interface DbContext {
  db: DrizzleDB;
  organizationId: string | null;
  userId: string | null;
  bypassRls: boolean;
}

// Database context creation function (now runs during DI with middleware-extracted session)
async function createDbContext(
  connectionManager: ConnectionManager,
  request: Request,
): Promise<DbContext> {
  // Extract organization ID, user ID and bypass flag from request
  // This assumes auth middleware has set these values
  // TODO: VALIDATION - Add organization ID format validation to prevent invalid IDs
  let organizationId =
    (request as any).organizationId ||
    (request.headers['x-organization-id'] as string);

  // Also check URL parameters for organization ID (for organization-specific endpoints)
  if (!organizationId && request.params?.organizationId) {
    organizationId = request.params.organizationId;
  }

  // Extract user ID from session context (set by middleware before DI)
  let userId: string | null = null;

  console.log(`🔍 [DI] Session context check:`, {
    hasUserSession: !!(request as any).userSession,
    userSessionKeys: (request as any).userSession
      ? Object.keys((request as any).userSession)
      : [],
    userSessionUser: (request as any).userSession?.user,
    userSessionUserId: (request as any).userSession?.user?.id,
  });

  if ((request as any).userSession?.user?.id) {
    userId = (request as any).userSession.user.id;
    console.log(`✅ [DI] Extracted userId from session: ${userId}`);
  }

  // Fallback to request object or header
  if (!userId) {
    // TODO: SECURITY - Validate user ID format and ensure it matches the authenticated user to prevent header spoofing
    userId =
      (request as any).userId || (request.headers['x-user-id'] as string);
    if (userId) {
      console.log(
        `✅ [DI] Extracted userId from request object/header: ${userId}`,
      );
    }
  }

  // TODO: SECURITY - Add proper authorization checks for bypass operations to prevent unauthorized RLS bypass
  const bypassRls =
    (request as any).bypassRls || request.headers['x-bypass-rls'] === 'true';

  console.log(`🌐 [DI] Request context:`, {
    organizationId,
    userId,
    bypassRls,
    url: request.url,
    method: request.method,
  });

  // Get the base database connection - RLS configuration is handled by repository layer
  const db = await connectionManager.getDb();

  console.log(`✅ [DI] Database context created successfully`);

  return {
    db,
    organizationId,
    userId,
    bypassRls,
  };
}

@Global()
@Module({
  providers: [
    // Global singleton connection manager
    {
      provide: DRIZZLE_CONNECTION_MANAGER,
      useFactory: () => {
        console.log('🏗️ [MODULE] Creating Drizzle connection manager...');

        const hasRLS =
          process.env.NODE_ENV === 'production' ||
          process.env.ENABLE_RLS === 'true';

        console.log('🏗️ [MODULE] RLS configuration:', {
          hasRLS,
          nodeEnv: process.env.NODE_ENV,
          enableRLS: process.env.ENABLE_RLS,
        });

        // createConnectionManager will automatically prefer DATABASE_URL_RLS_USER
        const connectionManager = createConnectionManager(
          process.env.DATABASE_URL_RLS_USER,
          hasRLS,
        );

        console.log('✅ [MODULE] Connection manager created successfully');

        return connectionManager;
      },
    },

    // Database context is now created lazily in repository factories
    // This ensures it runs after guards have set the session context
    //
    // // Request-scoped database context (REMOVED - using lazy creation instead)
    {
      provide: DRIZZLE_DB_CONTEXT,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ): Promise<DbContext> => {
        // Extract organization ID, user ID and bypass flag from request
        // This assumes auth middleware has set these values
        const organizationId =
          (request as any).organizationId ||
          (request.headers['x-organization-id'] as string);

        // Extract user ID from multiple sources in order of preference:
        // 1. Session context (from JWT token in cookies)
        // 2. Request object (set by middleware)
        // 3. Header (for API clients)
        let userId: string | null = null;

        // First, try to get from session context
        console.log(`🔍 [API] Session context check:`, {
          hasUserSession: !!(request as any).userSession,
          userSessionKeys: (request as any).userSession
            ? Object.keys((request as any).userSession)
            : [],
          userSessionUser: (request as any).userSession?.user,
          userSessionUserId: (request as any).userSession?.user?.id,
        });

        if ((request as any).userSession?.user?.id) {
          userId = (request as any).userSession.user.id;
          console.log(`✅ [API] Extracted userId from session: ${userId}`);
        }

        // Fallback to request object or header
        if (!userId) {
          userId =
            (request as any).userId || (request.headers['x-user-id'] as string);
          if (userId) {
            console.log(
              `✅ [API] Extracted userId from request object/header: ${userId}`,
            );
          }
        }

        // Final check - if still no userId, log what we have
        if (!userId) {
          console.log(`❌ [API] No userId found in any source:`, {
            userSession: !!(request as any).userSession,
            requestUserId: !!(request as any).userId,
            headerUserId: !!request.headers['x-user-id'],
          });
        }

        // TODO: Investigate if we can do this as it can be a potential security risk
        const bypassRls =
          (request as any).bypassRls ||
          request.headers['x-bypass-rls'] === 'true';

        console.log(`🌐 [API] Request context:`, {
          organizationId,
          userId,
          bypassRls,
          url: request.url,
          method: request.method,
        });

        // Get the base database connection - RLS configuration is handled by repository layer
        const db = await connectionManager.getDb();

        console.log(`✅ [API] Database context created successfully`);

        return {
          db,
          organizationId,
          userId,
          bypassRls,
        };
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },

    // Repository factory that creates repositories lazily
    {
      provide: 'REPOSITORY_FACTORY',
      scope: Scope.REQUEST,
      useFactory: (connectionManager: ConnectionManager, request: Request) => {
        console.log(`🔍 [FACTORY] Creating repository factory`);

        return {
          async getOrganizationRepository(): Promise<OrganizationRepository> {
            console.log(
              `🔍 [FACTORY] Creating OrganizationRepository (truly lazy after guards)`,
            );
            const dbContext = await createDbContext(connectionManager, request);
            return new OrganizationRepository(dbContext.db);
          },
          async getUserRepository(): Promise<UserRepository> {
            console.log(
              `🔍 [FACTORY] Creating UserRepository (truly lazy after guards)`,
            );
            const dbContext = await createDbContext(connectionManager, request);
            return new UserRepository(dbContext.db);
          },
        };
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },

    // Keep the old providers for backward compatibility during testing
    {
      provide: OrganizationRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating OrganizationRepository with lazy db context (OLD WAY)`,
        );

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new OrganizationRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: UserRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating UserRepository with lazy db context (OLD WAY)`,
        );

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new UserRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: OrganizationModuleRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating OrganizationModuleRepository with lazy db context (OLD WAY)`,
        );

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new OrganizationModuleRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: MemberRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(`🔍 [REPO] Creating MemberRepository with lazy db context`);

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new MemberRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: RoleRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(`🔍 [REPO] Creating RoleRepository with lazy db context`);

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new RoleRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: PermissionRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating PermissionRepository with lazy db context`,
        );

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new PermissionRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: InviteRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(`🔍 [REPO] Creating InviteRepository with lazy db context`);

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new InviteRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: MemberActivityRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating MemberActivityRepository with lazy db context`,
        );

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new MemberActivityRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: ProductRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating ProductRepository with lazy db context`,
        );

        // Create database context lazily (after guards have run)
        const dbContext = await createDbContext(connectionManager, request);
        return new ProductRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: EmailAuthCodeRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating EmailAuthCodeRepository with lazy db context`,
        );
        const dbContext = await createDbContext(connectionManager, request);
        return new EmailAuthCodeRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: SubscriptionRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating SubscriptionRepository with lazy db context`,
        );
        const dbContext = await createDbContext(connectionManager, request);
        return new SubscriptionRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: SubscriptionTierRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating SubscriptionTierRepository with lazy db context`,
        );
        const dbContext = await createDbContext(connectionManager, request);
        return new SubscriptionTierRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: SubscriptionPlanRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(
          `🔍 [REPO] Creating SubscriptionPlanRepository with lazy db context`,
        );
        const dbContext = await createDbContext(connectionManager, request);
        return new SubscriptionPlanRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: PriceRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(`🔍 [REPO] Creating PriceRepository with lazy db context`);
        const dbContext = await createDbContext(connectionManager, request);
        return new PriceRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
    {
      provide: FileRepository,
      scope: Scope.REQUEST,
      useFactory: async (
        connectionManager: ConnectionManager,
        request: Request,
      ) => {
        console.log(`🔍 [REPO] Creating FileRepository with lazy db context`);
        const dbContext = await createDbContext(connectionManager, request);
        return new FileRepository(dbContext.db);
      },
      inject: [DRIZZLE_CONNECTION_MANAGER, REQUEST],
    },
  ],
  exports: [
    DRIZZLE_CONNECTION_MANAGER,
    OrganizationRepository,
    UserRepository,
    OrganizationModuleRepository,
    MemberRepository,
    RoleRepository,
    PermissionRepository,
    InviteRepository,
    MemberActivityRepository,
    ProductRepository,
    EmailAuthCodeRepository,
    SubscriptionRepository,
    SubscriptionTierRepository,
    SubscriptionPlanRepository,
    PriceRepository,
    FileRepository,
  ],
})
export class DatabaseDrizzleModule {
  /**
   * Static method to get connection manager for CLI usage or testing
   */
  static createConnectionManager(
    connectionString?: string,
    hasRLS?: boolean,
  ): ConnectionManager {
    const enableRLS =
      hasRLS ??
      (process.env.NODE_ENV === 'production' ||
        process.env.ENABLE_RLS === 'true');

    // Use the improved connection manager that handles RLS/ROOT user selection
    return createConnectionManager(connectionString, enableRLS);
  }

  /**
   * Test database connection on module initialization
   */
  async onModuleInit() {
    console.log('🚀 [MODULE] DatabaseDrizzleModule initializing...');

    try {
      const connectionManager = DatabaseDrizzleModule.createConnectionManager();
      const isConnected = await connectionManager.testConnection();

      console.log('🔍 [MODULE] Database connection test:', {
        success: isConnected,
        connectionString:
          connectionManager
            .getDatabaseConfig()
            .connectionString.substring(0, 20) + '...',
      });

      if (isConnected) {
        console.log('✅ [MODULE] Database connection verified');
      } else {
        console.error('❌ [MODULE] Database connection failed');
      }
    } catch (error) {
      console.error('❌ [MODULE] Database initialization error:', error);
    }
  }
}
