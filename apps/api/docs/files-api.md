# Files API Documentation

## Overview

The Files API provides endpoints for managing and retrieving files within an organization. This API follows the established patterns in the codebase for authentication, authorization, and data handling.

## Endpoint

### GET /v1/organizations/:organizationId/files

Retrieves files for an organization with optional filtering, sorting, and pagination.

#### Authentication

- Requires valid JWT token via `access_token` cookie
- Uses `CustomAuthGuard` for authentication
- Uses `RlsContextGuard` for Row Level Security context

#### Parameters

**Path Parameters:**
- `organizationId` (string, required): The organization ID

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20, max: 100)
- `sortBy` (string, optional): Sort field (default: "createdAt")
- `sortOrder` (enum, optional): Sort order - "asc" or "desc" (default: "desc")
- `search` (string, optional): Search in file names
- `documentType` (string[], optional): Filter by document types
- `accessLevel` (string[], optional): Filter by access levels
- `categoryId` (string[], optional): Filter by category IDs
- `scopeId` (string[], optional): Filter by scope IDs
- `fileType` (string[], optional): Filter by file types
- `createdById` (string[], optional): Filter by creator IDs
- `includeTrained` (boolean, optional): Include only trained files
- `excludeTrained` (boolean, optional): Exclude trained files
- `createdAfter` (string, optional): Filter files created after date (ISO string)
- `createdBefore` (string, optional): Filter files created before date (ISO string)

#### Response

```typescript
{
  files: FileDTO[];
  stats: FileStatsDTO;
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
```

**FileDTO Structure:**
```typescript
{
  id: string;
  name: string;
  path?: string;
  checksum?: string;
  organizationId: string;
  createdById?: string;
  createdAt: string;
  updatedAt: string;
  trainedAt?: string;
  documentType?: DocumentType;
  categoryId?: string;
  scopeId?: string;
  accessLevel?: FileAccessLevel;
  tags?: string[];
  fileType?: string;
  content?: string;
  url?: string;
  notes?: string;
  createdBy?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
  scope?: {
    id: string;
    name: string;
  };
}
```

**FileStatsDTO Structure:**
```typescript
{
  total: number;
  byDocumentType: Record<string, number>;
  byAccessLevel: Record<string, number>;
  trained: number;
  untrained: number;
  recentUploads: number;
}
```

#### Status Codes

- `200 OK`: Files retrieved successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions

#### Example Request

```bash
curl -X GET \
  "https://api.example.com/v1/organizations/org123/files?page=1&limit=10&search=policy&documentType=policy&documentType=procedure" \
  -H "Cookie: access_token=your_jwt_token"
```

#### Example Response

```json
{
  "files": [
    {
      "id": "file123",
      "name": "Security Policy.pdf",
      "organizationId": "org123",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z",
      "documentType": "policy",
      "accessLevel": "internal",
      "tags": ["security", "policy"],
      "fileType": "pdf"
    }
  ],
  "stats": {
    "total": 1,
    "byDocumentType": {
      "policy": 1
    },
    "byAccessLevel": {
      "internal": 1
    },
    "trained": 0,
    "untrained": 1,
    "recentUploads": 1
  },
  "total": 1,
  "page": 1,
  "limit": 10,
  "hasMore": false
}
```

## Implementation Details

### Architecture

The implementation follows the established patterns:

1. **Controller** (`files.controller.ts`): Handles HTTP requests, validates parameters, and returns responses
2. **Service** (`files.service.ts`): Contains business logic and data transformation
3. **Repository** (`file.repository.ts`): Handles database operations (existing)
4. **DTOs** (`files.ts`): Defines request/response contracts using Zod schemas

### Key Features

- **Pagination**: Supports page-based pagination with configurable limits
- **Filtering**: Multiple filter options for different file attributes
- **Sorting**: Configurable sorting by various fields
- **Statistics**: Provides aggregated statistics about files
- **Validation**: Request/response validation using Zod schemas
- **Security**: Row Level Security (RLS) integration
- **Error Handling**: Comprehensive error handling with appropriate HTTP status codes

### Database Integration

The API uses the existing `FileRepository.findByOrganization` method which:
- Enforces organization-level data isolation
- Supports filtering and pagination
- Uses Drizzle ORM for type-safe database operations
- Implements Row Level Security patterns

### Testing

To test the endpoint:

1. Ensure you have a valid JWT token
2. Make a GET request to the endpoint with appropriate organization ID
3. Verify the response structure matches the documented schema
4. Test various filter combinations and pagination scenarios

### Future Enhancements

Potential improvements for future iterations:
- Add support for file relations (createdBy, scope) in repository queries
- Implement caching for frequently accessed file lists
- Add support for bulk operations
- Implement file content search capabilities
- Add audit logging for file access
