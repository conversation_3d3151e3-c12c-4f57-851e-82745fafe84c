"use client";

import { getEnvByName } from "@/lib/utils-action";
import { useEffect, useState } from "react";
import { Organization } from "@/models/organization";
import { cn } from "@/lib/utils";
import { Menu } from "./menu";
import { AccessControl } from "~/src/models/other-data";
import { getMemberById } from "~/src/app/[lang]/(private)/[org]/members/server-actions/member-actions";
import { useSession } from "next-auth/react";
import { useHeaderHeight } from "@/context/header-height-context";
import { SIDENAV_WIDTHS } from "@/context/sidenav-context";
import { useSidenav } from "@/context/sidenav-context";

type SideNavProps = {
  currentOrg: Organization;
  selectedItem: string;
  setSelectedItem: React.Dispatch<React.SetStateAction<string>>;
  isOpen?: boolean;
  setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  isHovering?: boolean;
  setIsHovering?: React.Dispatch<React.SetStateAction<boolean>>;
};

export function SideNav({
  currentOrg,
  selectedItem,
  setSelectedItem,
}: SideNavProps) {
  const { height } = useHeaderHeight();
  const { isOpen, setIsOpen, isHovering, setIsHovering, showSidebar } =
    useSidenav();
  const DEFAULT_HEADER_HEIGHT = 64; // Or your actual default/SSR header height
  const [currentVersion, setCurrentVersion] = useState("0.0.0");
  const { status: sessionStatus, data: session } = useSession({
    required: true,
  });
  const [memberAccessControl, setMemberAccessControl] = useState<
    AccessControl[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  useEffect(() => {
    const fetchEnvVar = async () => {
      const version = await getEnvByName("ASKINFOSEC_VERSION");
      setCurrentVersion(version ?? "0.0.0");
    };
    fetchEnvVar();
  }, []);
  useEffect(() => {
    const fetchMemberAccessControl = async () => {
      setIsLoading(true);
      try {
        if (currentOrg && session?.user.id) {
          if (currentOrg.members.length > 0) {
            const memberId = currentOrg.members.find(
              (member: any) => member.user_id === session?.user.id,
            )?.id;

            if (memberId) {
              const membersAccess: AccessControl[] = (
                await getMemberById(currentOrg.id, session?.user.id!, memberId)
              ).accessDetail;
              setMemberAccessControl(membersAccess);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching member access:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchMemberAccessControl();
  }, [currentOrg, session?.user.id]);

  // if (isLoading) {
  //   return <Loader />;
  // }

  // If showSidebar is false, don't render the sidebar
  if (!showSidebar) {
    return null;
  }

  return (
    <aside
      className={cn(
        // Base styles
        "fixed left-0 h-screen bg-sidebar border-r shadow-md",
        // Z-index and transitions
        "z-40 transition-all duration-500 ease-in-out",
        // Border styles
        "border-r-2 border-transparent hover:border-primary/20",
        // Responsive styles - hidden by default, show on md and up
        "hidden md:flex md:flex-col",
        // Show on sheet mode
        "sheet:block sheet:relative sheet:h-[100dvh] sheet:border-none sheet:w-full",
      )}
      style={{
        // Ensure height and top are stable for SSR and initial client render
        height: `calc(100vh - ${!hasMounted ? DEFAULT_HEADER_HEIGHT : height}px)`,
        top: `${!hasMounted ? DEFAULT_HEADER_HEIGHT : height}px`,
        width: !hasMounted
          ? `${SIDENAV_WIDTHS.COLLAPSED}px`
          : isHovering && !isOpen
            ? `${SIDENAV_WIDTHS.EXPANDED}px`
            : isOpen
              ? `${SIDENAV_WIDTHS.EXPANDED}px`
              : `${SIDENAV_WIDTHS.COLLAPSED}px`,
      }}
      onMouseEnter={() => !isOpen && setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <button
        className="shrink-0 hover:bg-sidebar-hover/20 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:bg-sidebar-hover/20 py-3 my-3 w-full transition-colors duration-200"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={isOpen ? "Collapse navigation menu" : "Expand navigation menu"}
        aria-expanded={isOpen}
        type="button"
      >
        <div className="flex justify-center gap-1 w-[48px] mx-auto">
          <svg
            width="8"
            height="12"
            viewBox="0 0 8 12"
            xmlns="http://www.w3.org/2000/svg"
            className={`${
              (isHovering && !isOpen) || isOpen ? "" : "rotate-180"
            } transition-all duration-500 fill-current text-foreground`}
            aria-hidden="true"
          >
            <path
              d="M7.00535 1.35917C7.06488 1.41862 7.11212 1.48922 7.14437 1.56694C7.17667 1.64477 7.19329 1.7282 7.19329 1.81247C7.19329 1.89673 7.17667 1.98017 7.14437 2.058C7.11211 2.13574 7.06485 2.20636 7.00529 2.26582L7.00535 1.35917ZM7.00535 1.35917C6.94588 1.29959 6.87525 1.25233 6.7975 1.22006C6.71967 1.18777 6.63623 1.17114 6.55197 1.17114C6.4677 1.17114 6.38427 1.18777 6.30644 1.22006C6.2287 1.25232 6.15809 1.29957 6.09863 1.35913C6.09855 1.3592 6.09848 1.35928 6.09841 1.35935L1.41135 6.0464C1.41128 6.04647 1.4112 6.04656 1.41113 6.04663C1.35157 6.10609 1.30432 6.1767 1.27206 6.25444C1.23977 6.33227 1.22314 6.4157 1.22314 6.49997C1.22314 6.58423 1.23977 6.66767 1.27206 6.7455C1.30432 6.82323 1.35157 6.89384 1.41113 6.95331C1.4112 6.95338 1.41128 6.95345 1.41135 6.95353L6.09863 11.6408C6.21886 11.761 6.38193 11.8286 6.55197 11.8286C6.722 11.8286 6.88507 11.761 7.00531 11.6408C7.12554 11.5206 7.19309 11.3575 7.19309 11.1875C7.19309 11.0174 7.12554 10.8544 7.00531 10.7341L7.00529 10.7341L2.7704 6.49997L7.00508 2.26603V1.3589C7.00517 1.35899 7.00526 1.35908 7.00535 1.35917Z"
              className="fill-current text-foreground"
              strokeWidth="0.5"
            />
          </svg>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            xmlns="http://www.w3.org/2000/svg"
            className="fill-current text-foreground"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              className="fill-current text-foreground"
              d="M0.458496 1.1875C0.458496 1.70527 0.878229 2.125 1.396 2.125H12.021C12.5388 2.125 12.9585 1.70527 12.9585 1.1875C12.9585 0.669733 12.5388 0.25 12.021 0.25H1.396C0.878229 0.25 0.458496 0.669733 0.458496 1.1875ZM0.458496 11.1875C0.458496 11.7053 0.878229 12.125 1.396 12.125H12.021C12.5388 12.125 12.9585 11.7053 12.9585 11.1875C12.9585 10.6697 12.5388 10.25 12.021 10.25H1.396C0.878229 10.25 0.458496 10.6697 0.458496 11.1875ZM1.396 7.125C0.878229 7.125 0.458496 6.70527 0.458496 6.1875C0.458496 5.66973 0.878229 5.25 1.396 5.25H12.021C12.5388 5.25 12.9585 5.66973 12.9585 6.1875C12.9585 6.70527 12.5388 7.125 12.021 7.125H1.396Z"
            />
          </svg>
        </div>
      </button>
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Scrollable menu area */}
        <Menu
          isOpen={isOpen}
          isHovering={isHovering}
          currentOrg={currentOrg}
          memberAccessControl={memberAccessControl}
          selectedItem={selectedItem}
          setSelectedItem={setSelectedItem}
        />
      </div>
    </aside>
  );
}
