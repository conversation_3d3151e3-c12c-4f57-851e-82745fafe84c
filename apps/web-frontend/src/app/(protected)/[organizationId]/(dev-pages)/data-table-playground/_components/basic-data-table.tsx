"use client";

import { useMemo, useState } from "react";
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { DataTable } from "@askinfosec/shadcn-ui/components/data-table/data-table";
import { DataTableToolbar } from "@askinfosec/shadcn-ui/components/data-table/data-table-toolbar";
import { DataTableActionBar } from "@askinfosec/shadcn-ui/components/data-table/data-table-action-bar";
import { DataTableActionBarAction } from "@askinfosec/shadcn-ui/components/data-table/data-table-action-bar";
import { DataTableActionBarSelection } from "@askinfosec/shadcn-ui/components/data-table/data-table-action-bar";
import { Checkbox } from "@askinfosec/shadcn-ui/components/ui/checkbox";
import { Trash2 } from "lucide-react";

// Define your data type
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: "active" | "inactive";
  createdAt: Date;
}

// Sample data
const users: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    createdAt: new Date("2024-01-15"),
  },
  // ... more users
];

export function BasicDataTable() {
  const [data, setData] = useState(users);

  // Define columns with metadata for filtering
  const columns: ColumnDef<User>[] = useMemo(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
        minSize: 40,
        maxSize: 40,
      },
      {
        accessorKey: "name",
        header: "Name",
        meta: {
          label: "Name",
          variant: "text",
          placeholder: "Search names...",
        },
      },
      {
        accessorKey: "email",
        header: "Email",
        meta: {
          label: "Email",
          variant: "text",
          placeholder: "Search emails...",
        },
      },
      {
        accessorKey: "role",
        header: "Role",
        meta: {
          label: "Role",
          variant: "select",
          options: [
            { label: "Admin", value: "admin" },
            { label: "User", value: "user" },
            { label: "Moderator", value: "moderator" },
          ],
        },
      },
      {
        accessorKey: "status",
        header: "Status",
        meta: {
          label: "Status",
          variant: "select",
          options: [
            { label: "Active", value: "active" },
            { label: "Inactive", value: "inactive" },
          ],
        },
      },
      {
        accessorKey: "createdAt",
        header: "Created At",
        meta: {
          label: "Created At",
          variant: "date",
        },
        cell: ({ row }) => {
          return new Date(row.getValue("createdAt")).toLocaleDateString();
        },
      },
    ],
    [],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
  });

  const handleDeleteSelected = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => row.original.id);
    setData(data.filter((user) => !selectedIds.includes(user.id)));
    table.toggleAllRowsSelected(false);
  };

  return (
    <div className="space-y-4">
      <DataTable
        table={table}
        actionBar={
          <DataTableActionBar table={table}>
            <DataTableActionBarSelection table={table} />
            <DataTableActionBarAction
              onClick={handleDeleteSelected}
              tooltip="Delete selected users"
            >
              <Trash2 />
              Delete
            </DataTableActionBarAction>
          </DataTableActionBar>
        }
      >
        <DataTableToolbar table={table} />
      </DataTable>
    </div>
  );
}
