"use client";

import { useSession } from "@/providers/session-provider";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@askinfosec/shadcn-ui/components";
import { Button, Badge } from "@askinfosec/shadcn-ui/components";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@askinfosec/shadcn-ui/components/ui/drawer";
import { Drawer as VaulDrawer } from "vaul";
import { Input } from "@askinfosec/shadcn-ui/components/ui/input";
import { Label } from "@askinfosec/shadcn-ui/components/ui/label";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";
import CssDebugger from "@/app/css-debugger";

export default function TestComponentsPage() {
  const { session, isLoading } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !session) {
      router.push("/sign-in");
    }
  }, [session, isLoading, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect via useEffect
  }

  return (
    <>
      <PageMetadataSetter
        title="Test Components"
        description="Testing shadcn-ui components in web-frontend"
      />
      <div className="space-y-8">
        {/* Debug Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">
            Debug: CSS Variables Check
          </h2>
          <Card>
            <CardHeader>
              <CardTitle>CSS Variables Status</CardTitle>
              <CardDescription>
                Check if shadcn-ui CSS variables are loaded
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CssDebugger />
            </CardContent>
          </Card>
        </div>

        {/* CSS Source Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">
            CSS Source Information
          </h2>
          <Card>
            <CardHeader>
              <CardTitle>Loaded CSS Files</CardTitle>
              <CardDescription>
                Information about the CSS files currently loaded
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Primary CSS Source:</h4>
                  <div className="p-3 bg-muted rounded-lg">
                    <code className="text-sm font-mono">
                      apps/web-frontend/src/app/globals.css
                    </code>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Contains ShadCN variables in :root and .dark selectors
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">CSS Import Method:</h4>
                  <div className="p-3 bg-muted rounded-lg">
                    <code className="text-sm font-mono">
                      @import "tailwindcss";
                    </code>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Direct TailwindCSS import with inline variables
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">CSS Variable Structure:</h4>
                <div className="p-3 bg-muted rounded-lg space-y-2">
                  <div className="text-sm">
                    <span className="font-mono text-primary">:root</span>
                    <span className="text-muted-foreground ml-2">
                      - Light mode ShadCN variables
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="font-mono text-primary">.dark</span>
                    <span className="text-muted-foreground ml-2">
                      - Dark mode ShadCN variables
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="font-mono text-primary">
                      @theme inline
                    </span>
                    <span className="text-muted-foreground ml-2">
                      - TailwindCSS v4 color mappings
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Key Variables:</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                  <div className="p-2 bg-muted rounded">
                    <div className="font-mono text-primary">--primary</div>
                    <div className="text-muted-foreground">275 55% 35%</div>
                  </div>
                  <div className="p-2 bg-muted rounded">
                    <div className="font-mono text-primary">--background</div>
                    <div className="text-muted-foreground">0 0% 100%</div>
                  </div>
                  <div className="p-2 bg-muted rounded">
                    <div className="font-mono text-primary">--foreground</div>
                    <div className="text-muted-foreground">240 10% 3.9%</div>
                  </div>
                  <div className="p-2 bg-muted rounded">
                    <div className="font-mono text-primary">--radius</div>
                    <div className="text-muted-foreground">0.5rem</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Buttons Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Buttons</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="default">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="destructive">Destructive</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link</Button>
          </div>
        </div>

        {/* Cards Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Card</CardTitle>
                <CardDescription>Simple title & description.</CardDescription>
              </CardHeader>
              <CardContent>
                <p>
                  Cards provide a flexible & extensible content container with
                  multiple variants & options.
                </p>
                <div className="mt-4">
                  <Button size="sm">Action</Button>
                </div>
              </CardContent>
            </Card>

            <Card className="border-dashed">
              <CardHeader>
                <CardTitle>Dashed Border</CardTitle>
                <CardDescription>
                  Demonstrates custom className usage.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>
                  The className prop can be used to tweak appearance while
                  retaining base styles.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Badges Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Badges</h2>
          <div className="flex flex-wrap gap-4">
            <Badge variant="default">Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
        </div>

        {/* Progress Bar Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Progress Bar</h2>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">ISO 27001</span>
                <span className="text-sm text-muted-foreground">
                  92% Compliant
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: "92%" }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Status Indicators</h2>
          <div className="flex flex-wrap gap-4 items-center">
            <Badge variant="default">good</Badge>
            <span className="text-sm text-muted-foreground">
              Target: &lt; 4 hours
            </span>
          </div>
        </div>

        {/* Drawer Test Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Drawer Component Test</h2>
          <Card>
            <CardHeader>
              <CardTitle>Drawer Functionality Test</CardTitle>
              <CardDescription>
                Testing the drawer component to debug the overlay issue
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Click the button below to test the drawer component:
                </p>

                {/* Simple Drawer Test */}
                <Drawer>
                  <DrawerTrigger asChild>
                    <Button variant="outline">Open Simple Drawer</Button>
                  </DrawerTrigger>
                  <DrawerContent className="z-[100] bg-white border-2 border-red-500">
                    <DrawerHeader>
                      <DrawerTitle>Simple Drawer Test</DrawerTitle>
                    </DrawerHeader>
                    <div className="p-4 bg-yellow-100 border-2 border-blue-500">
                      <p className="text-lg font-bold text-red-600 mb-4">
                        🎉 DRAWER CONTENT IS VISIBLE! 🎉
                      </p>
                      <p className="text-sm text-muted-foreground mb-4">
                        This is a simple drawer test. If you can see this
                        content, the drawer is working correctly.
                      </p>
                      <div className="space-y-2">
                        <Label htmlFor="test-input">Test Input</Label>
                        <Input
                          id="test-input"
                          placeholder="Type something here..."
                        />
                      </div>
                      <div className="mt-4 flex gap-2">
                        <Button>Save</Button>
                        <Button variant="outline">Cancel</Button>
                      </div>
                    </div>
                  </DrawerContent>
                </Drawer>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Test with dropdown menu integration (like in documents table):
                </p>

                {/* Drawer with Dropdown Test */}
                <Drawer>
                  <div className="flex gap-2">
                    <DrawerTrigger asChild>
                      <Button variant="outline">Open Drawer from Button</Button>
                    </DrawerTrigger>
                  </div>

                  <DrawerContent>
                    <DrawerHeader>
                      <DrawerTitle>Dropdown Integration Test</DrawerTitle>
                    </DrawerHeader>
                    <div className="p-4">
                      <p className="text-lg font-bold text-green-600 mb-4">
                        🎉 DROPDOWN DRAWER IS WORKING! 🎉
                      </p>
                      <p className="text-sm text-muted-foreground mb-4">
                        This drawer simulates the dropdown menu integration from
                        the documents table.
                      </p>
                      <div className="space-y-2">
                        <Label htmlFor="doc-name">Document Name</Label>
                        <Input
                          id="doc-name"
                          placeholder="Enter document name..."
                        />
                      </div>
                      <div className="mt-4 flex gap-2">
                        <Button>Save Document</Button>
                        <Button variant="outline">Cancel</Button>
                      </div>
                    </div>
                  </DrawerContent>
                </Drawer>
              </div>

              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h4 className="font-medium text-sm mb-2">Debug Information:</h4>
                <div className="text-xs space-y-1 text-muted-foreground">
                  <div>
                    • If you see only overlay without content, there's a CSS or
                    z-index issue
                  </div>
                  <div>
                    • If the drawer doesn't open at all, there's a
                    JavaScript/vaul issue
                  </div>
                  <div>
                    • If content is cut off, there's a height/overflow issue
                  </div>
                  <div>• Check browser console for any JavaScript errors</div>
                  <div>
                    • The test drawer now has explicit z-index and colored
                    borders
                  </div>
                </div>
              </div>

              {/* Raw Vaul Test */}
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-2">Raw Vaul Test:</h4>
                <Button
                  onClick={() => {
                    console.log("Testing vaul directly...");
                    // Test if vaul is available
                    if (typeof window !== "undefined") {
                      console.log("Window available");
                      // Try to find any vaul elements
                      const vaulElements =
                        document.querySelectorAll("[data-vaul-drawer]");
                      console.log("Vaul elements found:", vaulElements.length);
                      vaulElements.forEach((el, i) => {
                        console.log(`Element ${i}:`, el);
                      });
                    }
                  }}
                  variant="outline"
                >
                  Debug Vaul in Console
                </Button>
              </div>

              {/* Direct Vaul Test */}
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-2">Direct Vaul Test:</h4>
                <Button
                  onClick={async () => {
                    console.log("Testing direct vaul import...");
                    try {
                      // Try to import vaul directly
                      const { Drawer: VaulDrawer } = await import("vaul");
                      console.log("Vaul imported successfully:", VaulDrawer);

                      // Check if we can create a drawer instance
                      const drawer = VaulDrawer.Root({});
                      console.log("Drawer instance created:", drawer);
                    } catch (error) {
                      console.error("Error importing vaul:", error);
                    }
                  }}
                  variant="outline"
                >
                  Test Direct Vaul Import
                </Button>
              </div>

              {/* Raw Vaul Drawer Test */}
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-2">
                  Raw Vaul Drawer Test:
                </h4>
                <VaulDrawer.Root>
                  <VaulDrawer.Trigger asChild>
                    <Button variant="outline">Open Raw Vaul Drawer</Button>
                  </VaulDrawer.Trigger>
                  <VaulDrawer.Portal>
                    <VaulDrawer.Overlay className="fixed inset-0 bg-black/50 z-50" />
                    <VaulDrawer.Content className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 rounded-t-lg p-4">
                      <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-gray-300 mb-4" />
                      <div className="text-center">
                        <h3 className="text-lg font-semibold mb-2">
                          Raw Vaul Drawer
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          This is a raw vaul drawer test. If you can see this,
                          vaul is working!
                        </p>
                        <Button onClick={() => {}}>Close</Button>
                      </div>
                    </VaulDrawer.Content>
                  </VaulDrawer.Portal>
                </VaulDrawer.Root>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
