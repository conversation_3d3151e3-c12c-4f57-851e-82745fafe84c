"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@askinfosec/shadcn-ui/components/ui/sheet";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";
import { Edit, PanelLeft, PanelRight, X } from "lucide-react";
import { DocumentEditor } from "./document-editor";
import { DocumentDetailsPanel } from "./document-details-panel";
import { ContractsV1Files } from "@askinfosec/types";
import { cn } from "@/lib/utils";

interface DocumentEditorContainerProps {
  document: ContractsV1Files.FileDTO;
  organizationId: string;
  onSave?: (updatedDocument: Partial<ContractsV1Files.FileDTO>) => void;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const DocumentEditorContainer = ({
  document,
  organizationId,
  onSave,
  trigger,
  open,
  onOpenChange,
}: DocumentEditorContainerProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(true);

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen);
    } else {
      setIsOpen(newOpen);
    }
  };

  const handleSave = (updatedDocument: Partial<ContractsV1Files.FileDTO>) => {
    onSave?.(updatedDocument);
    handleOpenChange(false);
  };

  const handleCancel = () => {
    handleOpenChange(false);
  };

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  const isControlled = open !== undefined;
  const sheetOpen = isControlled ? open : isOpen;

  return (
    <Sheet open={sheetOpen} onOpenChange={handleOpenChange}>
      {!isControlled && (
        <SheetTrigger asChild>
          {trigger || (
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit Document
            </Button>
          )}
        </SheetTrigger>
      )}
      <SheetContent
        className={cn(
          "w-full max-w-none [&>button]:hidden transition-all duration-300 ease-in-out p-0",
          isPanelOpen ? "sm:w-[100vw]" : "sm:w-[75vw]",
        )}
        side="left"
      >
        <SheetHeader className="flex flex-row items-center justify-between p-4 pb-0">
          <SheetTitle className="text-lg font-semibold">
            Edit Document
          </SheetTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={togglePanel}
              className="h-8 w-8 p-0"
              title={isPanelOpen ? "Hide details panel" : "Show details panel"}
            >
              {isPanelOpen ? (
                <PanelRight className="h-4 w-4" />
              ) : (
                <PanelLeft className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleOpenChange(false)}
              className="h-8 w-8 p-0"
              title="Close editor"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </SheetHeader>

        <div className="flex h-[calc(100vh-8rem)] gap-4">
          {/* Editor Section */}
          <div
            className={cn(
              "flex-1 transition-all duration-300 ease-in-out",
              isPanelOpen ? "mr-0" : "mr-0",
            )}
          >
            <DocumentEditor
              document={document}
              organizationId={organizationId}
              onSave={handleSave}
              onCancel={handleCancel}
            />
          </div>

          {/* Details Panel */}
          <div
            className={cn(
              "transition-all duration-300 ease-in-out overflow-hidden",
              isPanelOpen ? "w-80 opacity-100" : "w-0 opacity-0",
            )}
          >
            {isPanelOpen && (
              <DocumentDetailsPanel document={document} className="h-full" />
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
