import { getServerSession } from "@/lib/auth-server";
import { redirect } from "next/navigation";
import {
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Shield,
  Users,
  Calendar,
  Search,
  Filter,
  ArrowRight,
  File<PERSON>heck,
  Book<PERSON>pen,
  ClipboardList,
} from "lucide-react";
import Link from "next/link";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";

export default async function DocumentsMetricsPage() {
  const session = await getServerSession();

  if (!session) {
    redirect("/sign-in");
  }

  return (
    <>
      <PageMetadataSetter
        title="Documents Hub"
        description="Comprehensive document management and compliance oversight for your organization."
      />
      <div className="space-y-6">
        {/* Compliance Status Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
              Compliance Status Overview
            </h2>
            <Shield className="h-5 w-5 text-blue-600" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Approved
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  142 documents
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Pending Review
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  8 documents
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Expired
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  3 documents
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Compliance Score
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">94%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Access Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link href="/policies" className="group">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-200 group-hover:border-blue-300 dark:group-hover:border-blue-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600">
                  Policies
                </h3>
                <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Total Policies
                  </span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    42
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Active
                  </span>
                  <span className="text-sm text-green-600">38</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Under Review
                  </span>
                  <span className="text-sm text-yellow-600">4</span>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/procedures" className="group">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-200 group-hover:border-green-300 dark:group-hover:border-green-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-green-600">
                  Procedures
                </h3>
                <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-green-600 transition-colors" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Total Procedures
                  </span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    28
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Active
                  </span>
                  <span className="text-sm text-green-600">25</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Under Review
                  </span>
                  <span className="text-sm text-yellow-600">3</span>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/general" className="group">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-200 group-hover:border-purple-300 dark:group-hover:border-purple-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-purple-600">
                  General Documents
                </h3>
                <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-purple-600 transition-colors" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Total Documents
                  </span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    86
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Templates
                  </span>
                  <span className="text-sm text-purple-600">51</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Guidelines
                  </span>
                  <span className="text-sm text-purple-600">35</span>
                </div>
              </div>
            </div>
          </Link>
        </div>

        {/* Documents Overview Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Documents Card */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Total Documents
              </h3>
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              156
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              +12 from last month
            </p>
          </div>

          {/* Recent Updates Card */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Recent Updates
              </h3>
              <Clock className="h-4 w-4 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              8
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              This week
            </p>
          </div>

          {/* Expiring Soon Card */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Expiring Soon
              </h3>
              <Calendar className="h-4 w-4 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              5
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Next 30 days
            </p>
          </div>

          {/* Contributors Card */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Active Contributors
              </h3>
              <Users className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              12
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              This month
            </p>
          </div>
        </div>

        {/* Documents Dashboard Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Documents */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Recent Documents
              </h3>
              <Link
                href="/documents/recent"
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                View all
              </Link>
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FileCheck className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Information Security Policy
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Updated 2 hours ago • Policy
                  </p>
                </div>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <ClipboardList className="h-5 w-5 text-green-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Incident Response Procedure
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Updated 1 day ago • Procedure
                  </p>
                </div>
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <BookOpen className="h-5 w-5 text-purple-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Data Classification Guidelines
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Updated 3 days ago • General
                  </p>
                </div>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </div>

          {/* Document Categories */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Document Categories
              </h3>
              <div className="flex space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Filter className="h-4 w-4 text-gray-400" />
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <FileCheck className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Policies
                  </span>
                </div>
                <span className="text-sm text-blue-600 dark:text-blue-400">
                  42 documents
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <ClipboardList className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    Procedures
                  </span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400">
                  28 documents
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <BookOpen className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                    Guidelines
                  </span>
                </div>
                <span className="text-sm text-purple-600 dark:text-purple-400">
                  35 documents
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    Templates
                  </span>
                </div>
                <span className="text-sm text-orange-600 dark:text-orange-400">
                  51 documents
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
