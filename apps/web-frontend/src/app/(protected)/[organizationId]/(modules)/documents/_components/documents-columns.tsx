"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@askinfosec/shadcn-ui/components/ui/checkbox";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@askinfosec/shadcn-ui/components/ui/dropdown-menu";
import { Ellipsis, Download, Trash2, Edit } from "lucide-react";
import { toast } from "sonner";
import { ContractsV1Files } from "@askinfosec/types";
import { downloadDocument } from "../_actions/download-document";
import { DocumentEditorContainer } from "./document-editor/document-editor-container";

interface CreateDocumentsColumnsProps {
  organizationId: string;
}

export const createDocumentsColumns = ({
  organizationId,
}: CreateDocumentsColumnsProps): ColumnDef<ContractsV1Files.FileDTO>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-0.5"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-0.5"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 40,
  },
  {
    accessorKey: "name",
    header: "Document Name",
    meta: {
      label: "Document Name",
      variant: "text",
      placeholder: "Search documents...",
    },
    cell: ({ row }) => {
      const name = row.getValue("name") as string;
      return (
        <div className="font-medium max-w-[200px] truncate" title={name}>
          {name}
        </div>
      );
    },
  },
  {
    accessorKey: "documentType",
    header: "Type",
    meta: {
      label: "Document Type",
      variant: "select",
      options: [
        { label: "Policy", value: "policy" },
        { label: "Procedure", value: "procedure" },
        { label: "Guideline", value: "guideline" },
        { label: "Standard", value: "standard" },
        { label: "Template", value: "template" },
        { label: "Form", value: "form" },
        { label: "Manual", value: "manual" },
        { label: "Other", value: "other" },
      ],
    },
    cell: ({ row }) => {
      const documentType = row.getValue("documentType") as string;
      return (
        <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
          {documentType || "Unknown"}
        </span>
      );
    },
  },
  {
    accessorKey: "accessLevel",
    header: "Access Level",
    meta: {
      label: "Access Level",
      variant: "select",
      options: [
        { label: "Public", value: "public" },
        { label: "Internal", value: "internal" },
        { label: "Confidential", value: "confidential" },
        { label: "Restricted", value: "restricted" },
      ],
    },
    cell: ({ row }) => {
      const accessLevel = row.getValue("accessLevel") as string;
      const getAccessLevelColor = (level: string) => {
        switch (level) {
          case "public":
            return "bg-green-50 text-green-700 ring-green-600/20";
          case "internal":
            return "bg-yellow-50 text-yellow-700 ring-yellow-600/20";
          case "confidential":
            return "bg-orange-50 text-orange-700 ring-orange-600/20";
          case "restricted":
            return "bg-red-50 text-red-700 ring-red-600/20";
          default:
            return "bg-gray-50 text-gray-700 ring-gray-600/20";
        }
      };

      return (
        <span
          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset ${getAccessLevelColor(accessLevel)}`}
        >
          {accessLevel || "Unknown"}
        </span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    meta: {
      label: "Created Date",
      variant: "date",
    },
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as string;
      return (
        <div className="text-sm text-gray-600">
          {new Date(createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    accessorKey: "trainedAt",
    header: "Training Status",
    meta: {
      label: "Training Status",
      variant: "select",
      options: [
        { label: "Trained", value: "trained" },
        { label: "Untrained", value: "untrained" },
      ],
    },
    cell: ({ row }) => {
      const trainedAt = row.getValue("trainedAt") as string | null;
      const isTrained = !!trainedAt;

      return (
        <span
          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset ${
            isTrained
              ? "bg-green-50 text-green-700 ring-green-600/20"
              : "bg-gray-50 text-gray-700 ring-gray-600/20"
          }`}
        >
          {isTrained ? "Trained" : "Untrained"}
        </span>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const handleDownload = async () => {
        try {
          const result = await downloadDocument(
            organizationId,
            row.original.id,
          );

          if (!result.success) {
            toast.error(result.error || "Failed to download document");
            return;
          }

          if (result.data) {
            // Convert base64 string to blob and download
            const { buffer, filename } = result.data;

            // Convert base64 to binary string using browser's atob
            const binaryString = window.atob(buffer);
            const bytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes], {
              type: "application/octet-stream",
            });
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement("a");
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast.success("Document downloaded successfully");
          }
        } catch (error) {
          console.error("Download error:", error);
          toast.error("Failed to download document");
        }
      };

      const handleDelete = () => {
        // TODO: Implement delete functionality
        console.log("Delete document:", row.original.id);
      };

      const handleSave = (
        updatedDocument: Partial<ContractsV1Files.FileDTO>,
      ) => {
        // TODO: Implement save functionality
        console.log("Save document:", row.original.id, updatedDocument);
        toast.success("Document updated successfully");
      };

      return (
        <div className="flex items-center gap-1.5">
          <DocumentEditorContainer
            document={row.original}
            organizationId={organizationId}
            onSave={handleSave}
            trigger={
              <Button
                aria-label="Edit document"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Edit className="h-4 w-4" />
              </Button>
            }
          />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-label="Open menu"
                variant="ghost"
                className="flex size-8 p-0 data-[state=open]:bg-muted"
              >
                <Ellipsis className="size-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuItem onSelect={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onSelect={handleDelete}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
    size: 80,
  },
];
