"use client";

import { ContractsV1Files } from "@askinfosec/types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@askinfosec/shadcn-ui/components/ui/card";
import { Badge } from "@askinfosec/shadcn-ui/components/ui/badge";
import { Separator } from "@askinfosec/shadcn-ui/components/ui/separator";
import { CalendarDays, FileText, Tag, Clock, Download } from "lucide-react";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";

interface DocumentDetailsPanelProps {
  document: ContractsV1Files.FileDTO;
  className?: string;
}

export const DocumentDetailsPanel = ({
  document,
  className,
}: DocumentDetailsPanelProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className={className}>
      <Card className="h-full flex flex-col">
        <CardHeader className="pb-3 flex-shrink-0">
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 flex-1 overflow-y-auto">
          {/* Basic Information */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Basic Information
            </h4>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <FileText className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium break-words">
                    {document.name}
                  </p>
                  <p className="text-xs text-muted-foreground">File name</p>
                </div>
              </div>

              {document.notes && (
                <div className="flex items-start gap-2">
                  <Tag className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm break-words">{document.notes}</p>
                    <p className="text-xs text-muted-foreground">Notes</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* File Properties */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              File Properties
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Type</p>
                <Badge variant="secondary" className="text-xs">
                  {document.fileType || document.documentType || "Unknown"}
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Access Level</p>
                <Badge variant="outline" className="text-xs">
                  {document.accessLevel || "Not set"}
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* Timestamps */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Timestamps
            </h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm">{formatDate(document.createdAt)}</p>
                  <p className="text-xs text-muted-foreground">Created</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm">{formatDate(document.updatedAt)}</p>
                  <p className="text-xs text-muted-foreground">Last modified</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Additional Information */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Additional Information
            </h4>
            <div className="space-y-2">
              {document.documentType && (
                <div className="flex items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">Document Type</p>
                    <Badge variant="outline" className="text-xs capitalize">
                      {document.documentType}
                    </Badge>
                  </div>
                </div>
              )}

              {document.tags && document.tags.length > 0 && (
                <div className="flex items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">Tags</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {document.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {document.createdBy && (
                <div className="flex items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">Created By</p>
                    <p className="text-xs text-muted-foreground">
                      {document.createdBy.name ||
                        document.createdBy.email ||
                        "Unknown"}
                    </p>
                  </div>
                </div>
              )}

              {document.scope && (
                <div className="flex items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">Scope</p>
                    <Badge variant="outline" className="text-xs">
                      {document.scope.name}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Actions
            </h4>
            <div className="flex flex-col gap-2">
              <Button variant="outline" size="sm" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Download Original
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
