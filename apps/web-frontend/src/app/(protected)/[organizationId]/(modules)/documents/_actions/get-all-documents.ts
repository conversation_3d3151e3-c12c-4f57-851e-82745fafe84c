import { apiGet<PERSON><PERSON> } from "@/lib/backend-api";
import { ContractsV1Files } from "@askinfosec/types";

export interface GetDocumentsFilters {
  search?: string;
  documentType?: string[];
  accessLevel?: string[];
  categoryId?: string[];
  scopeId?: string[];
  fileType?: string[];
  createdById?: string[];
  includeTrained?: boolean;
  excludeTrained?: boolean;
  createdAfter?: string;
  createdBefore?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export async function getAllDocuments(
  organizationId: string,
  filters?: GetDocumentsFilters,
) {
  try {
    // Build query parameters manually
    const queryParams: string[] = [];

    if (filters?.search)
      queryParams.push(`search=${encodeURIComponent(filters.search)}`);
    if (filters?.page) queryParams.push(`page=${filters.page}`);
    if (filters?.limit) queryParams.push(`limit=${filters.limit}`);
    if (filters?.sortBy)
      queryParams.push(`sortBy=${encodeURIComponent(filters.sortBy)}`);
    if (filters?.sortOrder) queryParams.push(`sortOrder=${filters.sortOrder}`);
    if (filters?.includeTrained !== undefined)
      queryParams.push(`includeTrained=${filters.includeTrained}`);
    if (filters?.excludeTrained !== undefined)
      queryParams.push(`excludeTrained=${filters.excludeTrained}`);
    if (filters?.createdAfter)
      queryParams.push(
        `createdAfter=${encodeURIComponent(filters.createdAfter)}`,
      );
    if (filters?.createdBefore)
      queryParams.push(
        `createdBefore=${encodeURIComponent(filters.createdBefore)}`,
      );

    // Handle array parameters
    if (filters?.documentType) {
      filters.documentType.forEach((type) =>
        queryParams.push(`documentType=${encodeURIComponent(type)}`),
      );
    }
    if (filters?.accessLevel) {
      filters.accessLevel.forEach((level) =>
        queryParams.push(`accessLevel=${encodeURIComponent(level)}`),
      );
    }
    if (filters?.categoryId) {
      filters.categoryId.forEach((id) =>
        queryParams.push(`categoryId=${encodeURIComponent(id)}`),
      );
    }
    if (filters?.scopeId) {
      filters.scopeId.forEach((id) =>
        queryParams.push(`scopeId=${encodeURIComponent(id)}`),
      );
    }
    if (filters?.fileType) {
      filters.fileType.forEach((type) =>
        queryParams.push(`fileType=${encodeURIComponent(type)}`),
      );
    }
    if (filters?.createdById) {
      filters.createdById.forEach((id) =>
        queryParams.push(`createdById=${encodeURIComponent(id)}`),
      );
    }

    const queryString =
      queryParams.length > 0 ? `?${queryParams.join("&")}` : "";
    const url = `/v1/organizations/${organizationId}/files${queryString}`;

    const response: ContractsV1Files.GetFilesResponse = await apiGetJson(url);
    return {
      files: response.files || [],
      stats: response.stats,
      total: response.total || 0,
      page: response.page || 1,
      limit: response.limit || 20,
      hasMore: response.hasMore || false,
      error: null,
    };
  } catch (error) {
    console.error("Error fetching documents:", error);
    return {
      files: [],
      stats: null,
      total: 0,
      page: 1,
      limit: 20,
      hasMore: false,
      error: "Failed to load documents. Please try again.",
    };
  }
}

// Server-side version for use in server components
export async function getAllDocumentsServer(
  organizationId: string,
  filters?: GetDocumentsFilters,
) {
  return getAllDocuments(organizationId, filters);
}
