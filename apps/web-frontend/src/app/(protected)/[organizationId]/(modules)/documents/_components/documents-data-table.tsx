"use client";

import { useState, useEffect } from "react";
import {
  getCoreRowModel,
  useReactTable,
  RowSelectionState,
} from "@tanstack/react-table";
import { DataTable } from "@askinfosec/shadcn-ui/components/data-table/data-table";
import { DataTableToolbar } from "@askinfosec/shadcn-ui/components/data-table/data-table-toolbar";
import { DocumentsTableActionBar } from "./documents-table-action-bar";
import { createDocumentsColumns } from "./documents-columns";
import { ContractsV1Files } from "@askinfosec/types";
import {
  getAllDocuments,
  GetDocumentsFilters,
} from "../_actions/get-all-documents";

interface DocumentsDataTableProps {
  organizationId: string;
  initialFilters?: GetDocumentsFilters;
}

export function DocumentsDataTable({
  organizationId,
  initialFilters,
}: DocumentsDataTableProps) {
  const [data, setData] = useState<ContractsV1Files.FileDTO[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // Load documents on component mount
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        setLoading(true);
        const result = await getAllDocuments(organizationId, initialFilters);

        if (result.error) {
          setError(result.error);
        } else {
          setData(result.files);
          setError(null);
        }
      } catch (err) {
        setError("Failed to load documents");
        console.error("Error loading documents:", err);
      } finally {
        setLoading(false);
      }
    };

    loadDocuments();
  }, [organizationId, initialFilters]);

  // Get columns from the extracted columns file
  const columns = createDocumentsColumns({ organizationId });

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    state: {
      rowSelection,
    },
    onRowSelectionChange: (updater) => {
      const newSelection =
        typeof updater === "function" ? updater(rowSelection) : updater;

      // Log the selection change
      console.log("Row selection changed:", {
        previousSelection: rowSelection,
        newSelection,
        selectedRowIds: Object.keys(newSelection).filter(
          (key) => newSelection[key],
        ),
        selectedCount: Object.keys(newSelection).filter(
          (key) => newSelection[key],
        ).length,
        selectedDocuments: Object.keys(newSelection)
          .filter((key) => newSelection[key])
          .map((rowId) => {
            const row = data.find((_, index) => index.toString() === rowId);
            return row ? { id: row.id, name: row.name } : null;
          })
          .filter(Boolean),
      });

      setRowSelection(newSelection);
    },
  });

  const handleDeleteSelected = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => row.original.id);
    setData(data.filter((doc) => !selectedIds.includes(doc.id)));
    setRowSelection({});
  };

  const handleDownloadSelected = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => row.original.id);

    // TODO: Implement download functionality
    console.log("Downloading documents:", selectedIds);
    setRowSelection({});
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-sm text-gray-500">Loading documents...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-sm text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <DataTable
        table={table}
        actionBar={
          <DocumentsTableActionBar
            table={table}
            onDeleteSelected={handleDeleteSelected}
            onDownloadSelected={handleDownloadSelected}
          />
        }
        className="space-y-4"
      >
        <DataTableToolbar table={table} />
      </DataTable>
    </div>
  );
}
