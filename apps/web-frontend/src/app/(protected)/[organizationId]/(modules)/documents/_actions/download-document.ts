"use server";

import { api<PERSON><PERSON><PERSON><PERSON> } from "@/lib/backend-api";
import { ContractsV1Files } from "@askinfosec/types";

interface DownloadDocumentResult {
  success: boolean;
  error?: string;
  data?: {
    file: ContractsV1Files.FileDTO;
    buffer: string; // Base64 encoded string
    filename: string;
  };
}

export async function downloadDocument(
  organizationId: string,
  fileId: string,
): Promise<DownloadDocumentResult> {
  try {
    // Call the backend API to get the file data
    const response = await apiGetJson<ContractsV1Files.FileDTO>(
      `/v1/organizations/${organizationId}/files/${fileId}/download`,
    );

    if (!response) {
      return {
        success: false,
        error: "Failed to fetch file data",
      };
    }

    // Extract the buffer data from the file
    const bufferData = response.bufferFile;
    if (!bufferData) {
      return {
        success: false,
        error: "File buffer not found",
      };
    }

    // Extract file extension from path or name
    const getFileExtension = (filename: string): string => {
      const lastDot = filename.lastIndexOf(".");
      return lastDot !== -1 ? filename.substring(lastDot) : "";
    };

    const filename = response.name || "document";
    const extension = response.path
      ? getFileExtension(response.path)
      : getFileExtension(filename);
    const fullFilename = extension ? `${filename}${extension}` : filename;

    return {
      success: true,
      data: {
        file: response,
        buffer: bufferData,
        filename: fullFilename,
      },
    };
  } catch (error) {
    console.error("Error downloading document:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
