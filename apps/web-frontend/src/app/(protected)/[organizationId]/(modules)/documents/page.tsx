import { getServerSession } from "@/lib/auth-server";
import { redirect } from "next/navigation";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";
import { DocumentsDataTable } from "./_components/documents-data-table";
import { DataTableSkeleton } from "@askinfosec/shadcn-ui/components/data-table/data-table-skeleton";
import React from "react";
import { Shell } from "@/components/shell";

interface DocumentsPageProps {
  params: {
    organizationId: string;
  };
}

export default async function DocumentsPage({ params }: DocumentsPageProps) {
  const session = await getServerSession();
  const { organizationId } = await Promise.resolve(params);

  if (!session) {
    redirect("/sign-in");
  }

  return (
    <>
      <PageMetadataSetter
        title="Documents Hub"
        description="Comprehensive document management and compliance oversight for your organization."
      />

      <Shell className="gap-2">
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={5}
              filterCount={3}
              cellWidths={["20rem", "8rem", "10rem", "8rem", "10rem"]}
              shrinkZero
            />
          }
        >
          <DocumentsDataTable organizationId={organizationId} />
        </React.Suspense>
      </Shell>
    </>
  );
}
