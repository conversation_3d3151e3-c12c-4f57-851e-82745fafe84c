"use client";

import { ContractsV1Files } from "@askinfosec/types";
import { SimpleEditor } from "@/components/tiptap-templates/simple/simple-editor";

interface DocumentEditorProps {
  document: ContractsV1Files.FileDTO;
  organizationId: string;
  onSave?: (updatedDocument: Partial<ContractsV1Files.FileDTO>) => void;
  onCancel?: () => void;
}

export const DocumentEditor = ({
  document: _document,
  organizationId: _organizationId,
  onSave: _onSave,
  onCancel: _onCancel,
}: DocumentEditorProps) => {
  return <SimpleEditor />;
};
