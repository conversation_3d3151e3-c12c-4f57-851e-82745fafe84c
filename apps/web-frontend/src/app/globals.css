@import "tailwindcss";
@import "tw-animate-css";
/* react-day-picker base styles */
@import "react-day-picker/dist/style.css";
/* Tiptap styles */
@import "../styles/_variables.scss";
@import "../styles/_keyframe-animations.scss";

@custom-variant dark (&:is(.dark *));

/* AskInfosec Brand Colors - Light Mode */
:root {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Core shadcn-ui colors with AskInfosec branding - using oklch format for Tailwind v4 */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.35 0.15 275); /* AskInfosec Purple */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.96 0 0);
  --secondary-foreground: oklch(0.145 0 0);
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.46 0 0);
  --accent: oklch(0.95 0.05 275); /* Light purple accent */
  --accent-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.6 0.2 0);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.65 0 0);
  --radius: 0.5rem;

  /* AskInfosec specific colors */
  --active: 217 91% 60%;
  --active-foreground: 213 93% 95%;
  --ghost: 240 4.8% 95.9%;
  --ghost-foreground: 275 53% 37%;
  --sidebar-background: 275 30% 98%;
  --sidebar-divider: 275 10% 90%;
  --sidebar-hover: 275 40% 95%;
  --headernav-background: 275 20% 98%;
  --headernav-border: 275 10% 90%;
  --svg-fill-color: 240 10% 3.9%;

  /* Chart colors */
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;

  /* Gradient backgrounds */
  --signin: linear-gradient(
    297deg,
    #e5dced 0%,
    #fff 30.79%,
    #fff 65.37%,
    #e2d9ec 99.27%
  );
  --chat: 275 55% 35%;
  --chat-bg: 0 0% 100%;
  --chat-dropdown: 0 0% 100%;
  --chatbox-bg: 275 20% 98%;

  color-scheme: light;
}

.dark {
  /* Core shadcn-ui colors with AskInfosec dark mode branding - using oklch format */
  --background: oklch(0.06 0.05 270); /* Dark purple background */
  --foreground: oklch(0.91 0.02 213);
  --card: oklch(0.1 0.05 270);
  --card-foreground: oklch(0.98 0 0);
  --popover: oklch(0.06 0.05 270);
  --popover-foreground: oklch(0.91 0.02 213);
  --primary: oklch(0.45 0.15 275); /* Lighter purple for dark mode */
  --primary-foreground: oklch(0.91 0.02 213);
  --secondary: oklch(0.18 0.05 273);
  --secondary-foreground: oklch(0.91 0.02 213);
  --muted: oklch(0.15 0.05 270);
  --muted-foreground: oklch(0.7 0.02 266);
  --accent: oklch(0.2 0.05 273);
  --accent-foreground: oklch(0.91 0.02 213);
  --destructive: oklch(0.4 0.2 0);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.2 0.05 270);
  --input: oklch(0.16 0.02 240);
  --ring: oklch(0.25 0.05 273);

  /* AskInfosec specific colors - dark mode */
  --active: 217 91% 59%;
  --active-foreground: 213 93% 67%;
  --ghost: 273 25% 25%;
  --ghost-foreground: 213 31% 95%;
  --sidebar-background: 273 35% 8%;
  --sidebar-divider: 273 20% 15%;
  --sidebar-hover: 273 30% 15%;
  --headernav-background: 273 30% 10%;
  --headernav-border: 273 20% 15%;
  --svg-fill-color: 213 31% 91%;

  /* Chart colors - dark mode */
  --chart-1: 220 70% 60%;
  --chart-2: 160 60% 50%;
  --chart-3: 30 80% 60%;
  --chart-4: 280 65% 65%;
  --chart-5: 340 75% 60%;

  /* Gradient backgrounds - dark mode */
  --signin: linear-gradient(to right, #0e0e10, #151223, #261c4a);
  --chat: 273 40% 55%;
  --chat-bg: 281 20% 12%;
  --chat-dropdown: 270 20% 14%;
  --chatbox-bg: 270 20% 13%;

  color-scheme: dark;
}

/* Background utility class - matching original */
.background {
  background: var(--signin);
}

.background .dark {
  background: var(--signin);
}

/* TailwindCSS v4 inline theming - clean approach */
@theme inline {
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);

  /* Core shadcn-ui colors - automatically mapped by Tailwind v4 */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Chart colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Border radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Base styles */
@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* AskInfosec specific utilities */
  .bg-gradient-auth {
    background: var(--signin);
  }

  .bg-sidebar {
    background-color: hsl(var(--sidebar-background));
  }

  .bg-headernav {
    background-color: hsl(var(--headernav-background));
  }

  .border-headernav {
    border-color: hsl(var(--headernav-border));
  }

  .bg-ghost {
    background-color: hsl(var(--ghost));
  }

  .text-ghost-foreground {
    color: hsl(var(--ghost-foreground));
  }

  .bg-active {
    background-color: hsl(var(--active));
  }

  .text-active-foreground {
    color: hsl(var(--active-foreground));
  }

  /* SVG fill utility - matching original */
  .fill-svg {
    color: hsl(var(--svg-fill-color));
  }
}

/* Essential animations only */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation utility classes */
@layer utilities {
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-slide-in-from-top {
    animation: slide-in-from-top 0.3s ease-out;
  }

  .animate-slide-in-from-bottom {
    animation: slide-in-from-bottom 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.2s ease-out;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-in {
    animation: slideDown 0.2s ease-out;
  }

  .slide-in-from-top-5 {
    animation: slideDown 0.2s ease-out;
  }
}

/* Fallback for popover backgrounds - ensure they work with oklch format */
@layer components {
  .bg-popover {
    background-color: var(--popover);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }
}
