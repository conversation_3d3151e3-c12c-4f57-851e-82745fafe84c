"use client";

import React from "react";
import { DashboardLayoutProps } from "@/types/dashboard";
import { DashboardFooter } from "./dashboard-footer";
import { SidebarPageLayout } from "@/components/layout/sidebar-main-layout";
// import { RBACSecurityTest } from "@/components/rbac-security-test";

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  sidebar,
  mobileMenu,
  _headerActions,
  onMobileMenuToggle,
  onSidebarToggle,
  headerTitle,
  headerDescription,
  mobileMenuOpen = false,
}) => {
  return (
    <>
      <SidebarPageLayout
        sidebar={sidebar}
        mobileMenu={mobileMenu}
        _headerActions={_headerActions}
        onMobileMenuToggle={onMobileMenuToggle}
        onSidebarToggle={onSidebarToggle}
        headerTitle={headerTitle}
        headerDescription={headerDescription}
        mobileMenuOpen={mobileMenuOpen}
        pageFooter={<DashboardFooter />}
      >
        {children}
      </SidebarPageLayout>
      {/* <RBACSecurityTest /> */}
    </>
  );
};
