"use client";

import React from "react";
import { SidebarLayout } from "./sidebar-layout";
import { SidebarNav } from "./sidebar-nav";
import { SidebarNavItem } from "@/types/sidebar";

interface SidebarMobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  layoutType: string;
  items: SidebarNavItem[];
  footer?: React.ReactNode;
  currentPath?: string;
  isRouteActive?: (path?: string) => boolean;
  isChildRouteActive?: (path?: string) => boolean;
}

export function SidebarMobileMenu({
  isOpen,
  onClose,
  layoutType,
  items,
  footer,
  currentPath = "",
  isRouteActive = (p?: string) => !!p && currentPath.includes(p),
  isChildRouteActive = (p?: string) => !!p && currentPath.includes(p),
}: SidebarMobileMenuProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden" role="dialog" aria-modal="true" aria-label="Navigation menu">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-background/80 backdrop-blur-sm transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Sidebar Panel */}
      <div className="fixed left-0 top-0 h-full w-64 bg-card shadow-xl transform transition-transform duration-300 ease-in-out translate-x-0">
        <div className="flex flex-col h-full">
          <div className="flex-1 flex flex-col min-h-0">
            <SidebarLayout
              layoutType={layoutType}
              isCollapsed={false}
              isMobile
              onClose={onClose}
              footer={footer}
            >
              <SidebarNav
                items={items}
                isCollapsed={false}
                currentPath={currentPath}
                isRouteActive={isRouteActive}
                isChildRouteActive={isChildRouteActive}
              />
            </SidebarLayout>
          </div>
        </div>
      </div>
    </div>
  );
}
