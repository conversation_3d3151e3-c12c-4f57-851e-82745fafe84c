"use client";

import React from "react";
import { X, ArrowLeft } from "lucide-react";
import { cn } from "@/lib/utils";

interface SidebarHeaderProps {
  isCollapsed?: boolean;
  isMobile?: boolean;
  onClose?: () => void;
  showBack?: boolean;
  onBack?: () => void;
}

export function SidebarHeader({
  isCollapsed,
  isMobile,
  onClose,
  showBack,
  onBack,
}: SidebarHeaderProps) {
  return (
    <header className="flex items-center justify-between h-12 no-border flex-shrink-0 bg-background" role="banner">
      {isCollapsed ? (
        <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm mx-auto shadow-sm">
          A
        </div>
      ) : (
        <div className="flex items-center justify-between w-full px-4">
          <div className="flex items-center space-x-3">
            <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm shadow-sm">
              A
            </div>
            <span className="font-bold text-foreground text-lg">
              AskInfosec
            </span>
          </div>

          {showBack && (
            <button
              onClick={onBack}
              className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1 rounded-lg transition-colors duration-200"
              aria-label="Go back to dashboard"
              type="button"
            >
              <ArrowLeft className="h-5 w-5" aria-hidden="true" />
            </button>
          )}
        </div>
      )}

      {isMobile && onClose && (
        <button
          type="button"
          className={cn(
            "p-2 text-muted-foreground hover:text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1 rounded-lg transition-colors duration-200",
          )}
          onClick={onClose}
          aria-label="Close navigation sidebar"
        >
          <span className="sr-only">Close sidebar</span>
          <X className="h-5 w-5" aria-hidden="true" />
        </button>
      )}
    </header>
  );
}
