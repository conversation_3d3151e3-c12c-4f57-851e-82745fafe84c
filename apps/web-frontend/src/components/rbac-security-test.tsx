"use client";

import React, { useState } from "react";
import { useRBACSecurityTest } from "@/hooks/use-rbac-security-test";
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  ChevronDown,
  ChevronRight,
} from "lucide-react";

interface RBACSecurityTestProps {
  className?: string;
}

export function RBACSecurityTest({ className = "" }: RBACSecurityTestProps) {
  const { testResults, currentPermissions, runTests, isDevelopment } =
    useRBACSecurityTest();
  const [isCollapsed, setIsCollapsed] = useState(true); // Changed to true for minimized by default
  const [expandedTests, setExpandedTests] = useState<Set<string>>(new Set());

  // Only show in development
  if (!isDevelopment) {
    return null;
  }

  const toggleTestExpansion = (testName: string) => {
    const newExpanded = new Set(expandedTests);
    if (newExpanded.has(testName)) {
      newExpanded.delete(testName);
    } else {
      newExpanded.add(testName);
    }
    setExpandedTests(newExpanded);
  };

  const getStatusIcon = (status: "PASS" | "FAIL" | "WARNING") => {
    switch (status) {
      case "PASS":
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case "FAIL":
        return <XCircle className="w-4 h-4 text-red-600" />;
      case "WARNING":
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: "PASS" | "FAIL" | "WARNING") => {
    const baseClasses =
      "px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1";
    switch (status) {
      case "PASS":
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            {getStatusIcon(status)}
            PASS
          </span>
        );
      case "FAIL":
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            {getStatusIcon(status)}
            FAIL
          </span>
        );
      case "WARNING":
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            {getStatusIcon(status)}
            WARNING
          </span>
        );
    }
  };

  return (
    <div
      className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 w-80 bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Shield className="w-5 h-5 text-yellow-600" />
          <h3 className="font-semibold text-gray-900">
            RBAC Security Test Results
          </h3>
        </div>
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          {isCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </button>
      </div>

      {!isCollapsed && (
        <>
          {/* Test Results */}
          <div className="p-4 space-y-3 max-h-64 overflow-y-auto">
            {testResults.map((test) => (
              <div key={test.name} className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 text-sm">
                    {test.name}
                  </h4>
                  {getStatusBadge(test.status)}
                </div>
                <p className="text-gray-600 text-xs mb-2">{test.description}</p>
                {test.details && (
                  <div>
                    <button
                      onClick={() => toggleTestExpansion(test.name)}
                      className="flex items-center gap-1 text-gray-500 hover:text-gray-700 text-xs transition-colors"
                    >
                      {expandedTests.has(test.name) ? (
                        <ChevronDown className="w-3 h-3" />
                      ) : (
                        <ChevronRight className="w-3 h-3" />
                      )}
                      Details
                    </button>
                    {expandedTests.has(test.name) && (
                      <div className="mt-2 p-2 bg-white rounded border text-xs text-gray-700">
                        {test.details}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Permissions Summary */}
          <div className="px-4 pb-4">
            <div className="bg-blue-50 rounded-lg p-3">
              <h4 className="font-medium text-blue-900 text-sm mb-2">
                Current Permissions
              </h4>
              <div className="space-y-1">
                <div className="text-xs text-blue-800">
                  <strong>Role:</strong> {currentPermissions.role}
                </div>
                <div className="text-xs text-blue-800">
                  <strong>Permissions:</strong>{" "}
                  {currentPermissions.permissionsCount}
                </div>
                <div className="text-xs text-blue-800">
                  <strong>Department:</strong>{" "}
                  {currentPermissions.department || "None"}
                </div>
                {currentPermissions.isOwner && (
                  <div className="text-xs text-green-700 font-medium">
                    ✓ Owner Access
                  </div>
                )}
                {currentPermissions.isAdmin && (
                  <div className="text-xs text-blue-700 font-medium">
                    ✓ Admin Access
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Refresh Button */}
          <div className="px-4 pb-4">
            <button
              onClick={() => {
                runTests();
                // Force re-render by updating expanded tests
                setExpandedTests(new Set());
              }}
              className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              aria-label="Refresh security tests"
              type="button"
            >
              <RefreshCw className="w-4 h-4" aria-hidden="true" />
              Refresh Tests
            </button>
          </div>
        </>
      )}
    </div>
  );
}
