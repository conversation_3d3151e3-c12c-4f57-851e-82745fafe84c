/* Simple Editor Styles */
.simple-editor {
  height: 100%;
  display: flex;
  flex-direction: column;

  .ProseMirror {
    outline: none;
    padding: 1rem;
    flex: 1;
    overflow-y: auto;

    p {
      margin: 0.5rem 0;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin: 1rem 0 0.5rem 0;
      font-weight: 600;
    }

    h1 {
      font-size: 2rem;
    }
    h2 {
      font-size: 1.5rem;
    }
    h3 {
      font-size: 1.25rem;
    }

    ul,
    ol {
      padding-left: 1.5rem;
      margin: 0.5rem 0;
    }

    li {
      margin: 0.25rem 0;
    }

    blockquote {
      border-left: 4px solid #e5e7eb;
      padding-left: 1rem;
      margin: 1rem 0;
      font-style: italic;
      color: #6b7280;
    }

    code {
      background-color: #f3f4f6;
      padding: 0.125rem 0.25rem;
      border-radius: 0.25rem;
      font-family: "Courier New", monospace;
      font-size: 0.875rem;
    }

    pre {
      background-color: #f3f4f6;
      padding: 1rem;
      border-radius: 0.5rem;
      overflow-x: auto;
      margin: 1rem 0;

      code {
        background: none;
        padding: 0;
      }
    }
  }

  .toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
    flex-shrink: 0;

    .toolbar-group {
      display: flex;
      gap: 0.25rem;
      align-items: center;

      &:not(:last-child)::after {
        content: "";
        width: 1px;
        height: 1.5rem;
        background-color: #d1d5db;
        margin: 0 0.5rem;
      }
    }
  }

  .editor-container {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

/* Dark mode styles */
.dark .simple-editor {
  .ProseMirror {
    background-color: #1f2937;
    color: #f9fafb;

    blockquote {
      border-left-color: #4b5563;
      color: #9ca3af;
    }

    code {
      background-color: #374151;
      color: #f9fafb;
    }

    pre {
      background-color: #374151;
    }
  }

  .toolbar {
    background-color: #374151;
    border-bottom-color: #4b5563;

    .toolbar-group:not(:last-child)::after {
      background-color: #4b5563;
    }
  }

  .editor-container {
    background-color: #1f2937;
    border-color: #4b5563;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
