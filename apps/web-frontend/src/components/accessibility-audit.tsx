"use client";

import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>Circle, XCircle, Alert<PERSON><PERSON>gle, RefreshCw } from "lucide-react";

interface AccessibilityIssue {
  type: "error" | "warning" | "success";
  category: "color-contrast" | "focus-management" | "aria-labels" | "keyboard-navigation" | "semantic-html";
  element: string;
  description: string;
  wcagLevel: "A" | "AA" | "AAA";
  recommendation: string;
}

interface ColorContrastResult {
  element: string;
  foreground: string;
  background: string;
  ratio: number;
  passes: {
    aa: boolean;
    aaa: boolean;
  };
}

export function AccessibilityAudit() {
  const [issues, setIssues] = useState<AccessibilityIssue[]>([]);
  const [colorContrasts, setColorContrasts] = useState<ColorContrastResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [lastRun, setLastRun] = useState<Date | null>(null);

  // Function to calculate color contrast ratio
  const calculateContrastRatio = (color1: string, color2: string): number => {
    const getLuminance = (color: string): number => {
      const rgb = color.match(/\d+/g);
      if (!rgb) return 0;
      
      const [r, g, b] = rgb.map(val => {
        const c = parseInt(val) / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const lum1 = getLuminance(color1);
    const lum2 = getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  };

  // Function to run accessibility audit
  const runAccessibilityAudit = async () => {
    setIsRunning(true);
    const foundIssues: AccessibilityIssue[] = [];
    const contrastResults: ColorContrastResult[] = [];

    try {
      // Check for missing ARIA labels
      const buttonsWithoutLabels = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
      buttonsWithoutLabels.forEach((button, index) => {
        const hasVisibleText = button.textContent?.trim();
        if (!hasVisibleText) {
          foundIssues.push({
            type: "error",
            category: "aria-labels",
            element: `Button #${index + 1}`,
            description: "Button without accessible name",
            wcagLevel: "A",
            recommendation: "Add aria-label or visible text content"
          });
        }
      });

      // Check for missing focus indicators
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]');
      interactiveElements.forEach((element, index) => {
        const styles = window.getComputedStyle(element, ':focus-visible');
        const hasOutline = styles.outline !== 'none' && styles.outline !== '0px';
        const hasRing = styles.boxShadow.includes('ring') || styles.boxShadow.includes('focus');
        
        if (!hasOutline && !hasRing) {
          foundIssues.push({
            type: "warning",
            category: "focus-management",
            element: `${element.tagName.toLowerCase()} #${index + 1}`,
            description: "Interactive element may lack visible focus indicator",
            wcagLevel: "AA",
            recommendation: "Ensure focus-visible styles are properly applied"
          });
        }
      });

      // Check color contrast for text elements
      const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, button, a');
      const checkedElements = new Set<Element>();
      
      textElements.forEach((element, index) => {
        if (checkedElements.has(element)) return;
        checkedElements.add(element);
        
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;
        
        if (color && backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
          const ratio = calculateContrastRatio(color, backgroundColor);
          const fontSize = parseFloat(styles.fontSize);
          const fontWeight = styles.fontWeight;
          
          const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
          const aaThreshold = isLargeText ? 3 : 4.5;
          const aaaThreshold = isLargeText ? 4.5 : 7;
          
          contrastResults.push({
            element: `${element.tagName.toLowerCase()} #${index + 1}`,
            foreground: color,
            background: backgroundColor,
            ratio: Math.round(ratio * 100) / 100,
            passes: {
              aa: ratio >= aaThreshold,
              aaa: ratio >= aaaThreshold
            }
          });
          
          if (ratio < aaThreshold) {
            foundIssues.push({
              type: "error",
              category: "color-contrast",
              element: `${element.tagName.toLowerCase()} #${index + 1}`,
              description: `Color contrast ratio ${ratio.toFixed(2)}:1 fails WCAG AA (requires ${aaThreshold}:1)`,
              wcagLevel: "AA",
              recommendation: "Increase color contrast between text and background"
            });
          }
        }
      });

      // Check for proper heading hierarchy
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let lastLevel = 0;
      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        if (index === 0 && level !== 1) {
          foundIssues.push({
            type: "warning",
            category: "semantic-html",
            element: `${heading.tagName.toLowerCase()} #${index + 1}`,
            description: "Page should start with h1",
            wcagLevel: "AA",
            recommendation: "Use h1 for the main page heading"
          });
        } else if (level > lastLevel + 1) {
          foundIssues.push({
            type: "warning",
            category: "semantic-html",
            element: `${heading.tagName.toLowerCase()} #${index + 1}`,
            description: "Heading levels should not skip",
            wcagLevel: "AA",
            recommendation: "Use sequential heading levels (h1, h2, h3, etc.)"
          });
        }
        lastLevel = level;
      });

      // Check for images without alt text
      const images = document.querySelectorAll('img:not([alt])');
      images.forEach((img, index) => {
        foundIssues.push({
          type: "error",
          category: "aria-labels",
          element: `img #${index + 1}`,
          description: "Image without alt attribute",
          wcagLevel: "A",
          recommendation: "Add descriptive alt text or alt='' for decorative images"
        });
      });

      setIssues(foundIssues);
      setColorContrasts(contrastResults);
      setLastRun(new Date());
    } catch (error) {
      console.error('Accessibility audit failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    // Run initial audit after component mounts
    const timer = setTimeout(runAccessibilityAudit, 1000);
    return () => clearTimeout(timer);
  }, []);

  const getIssueIcon = (type: AccessibilityIssue['type']) => {
    switch (type) {
      case 'error':
        return <XCircle className="h-4 w-4 text-destructive" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getIssuesByCategory = (category: AccessibilityIssue['category']) => {
    return issues.filter(issue => issue.category === category);
  };

  const errorCount = issues.filter(i => i.type === 'error').length;
  const warningCount = issues.filter(i => i.type === 'warning').length;
  const passedContrasts = colorContrasts.filter(c => c.passes.aa).length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Accessibility Audit - WCAG 2.2 AA Compliance</CardTitle>
              <CardDescription>
                Comprehensive accessibility testing for keyboard navigation, color contrast, and screen reader support
                {lastRun && (
                  <span className="block text-xs text-muted-foreground mt-1">
                    Last run: {lastRun.toLocaleString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <Button
              onClick={runAccessibilityAudit}
              disabled={isRunning}
              variant="outline"
              size="sm"
              aria-label="Run accessibility audit"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
              {isRunning ? 'Running...' : 'Run Audit'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-destructive">{errorCount}</div>
              <div className="text-sm text-muted-foreground">Errors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{warningCount}</div>
              <div className="text-sm text-muted-foreground">Warnings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{passedContrasts}</div>
              <div className="text-sm text-muted-foreground">Passed Contrasts</div>
            </div>
          </div>

          {issues.length === 0 && !isRunning && (
            <div className="text-center py-8" role="status" aria-live="polite">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" aria-hidden="true" />
              <h3 className="text-lg font-semibold text-green-700">All Accessibility Checks Passed!</h3>
              <p className="text-muted-foreground">No accessibility issues found in the current page.</p>
            </div>
          )}

          {issues.length > 0 && (
            <div className="space-y-4">
              {(['error', 'warning'] as const).map(type => {
                const typeIssues = issues.filter(issue => issue.type === type);
                if (typeIssues.length === 0) return null;

                return (
                  <div key={type} className="space-y-2">
                    <h3 className="font-semibold flex items-center gap-2">
                      {getIssueIcon(type)}
                      {type === 'error' ? 'Errors' : 'Warnings'} ({typeIssues.length})
                    </h3>
                    <div className="space-y-2">
                      {typeIssues.map((issue, index) => (
                        <div key={index} className="border rounded-lg p-3 bg-muted/50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {issue.category.replace('-', ' ')}
                                </Badge>
                                <Badge variant="secondary" className="text-xs">
                                  WCAG {issue.wcagLevel}
                                </Badge>
                              </div>
                              <p className="font-medium text-sm">{issue.element}</p>
                              <p className="text-sm text-muted-foreground">{issue.description}</p>
                              <p className="text-xs text-blue-600 mt-1">{issue.recommendation}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
