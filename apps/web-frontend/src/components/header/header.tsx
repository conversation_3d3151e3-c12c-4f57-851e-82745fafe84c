"use client";

import React from "react";
import { OrganizationSwitcher } from "./organization-switcher";
import { AskAIButton } from "../ask-ai-button";

interface HeaderProps {
  showHamburgerMenu?: boolean;
  onMobileMenuToggle?: () => void;
  onSidebarToggle?: () => void;
  mobileMenuOpen?: boolean;
  headerActions?: React.ReactNode;
}

export function Header({
  showHamburgerMenu = false,
  onMobileMenuToggle,
  onSidebarToggle,
  mobileMenuOpen = false,
  headerActions,
}: HeaderProps = {}) {
  return (
    <header className="bg-background h-12 sticky top-0 transition-colors duration-200 flex-shrink-0 no-border">
      <div className="flex items-center justify-between px-4 h-full">
        {/* Left Section */}
        <div className="flex items-center space-x-3 flex-1">
          {/* Mobile Menu Button */}
          {showHamburgerMenu && (
            <button
              onClick={onMobileMenuToggle}
              className="lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:bg-muted/50 transition-all duration-200"
              aria-label={mobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              aria-expanded={mobileMenuOpen}
              type="button"
            >
              <span className="sr-only">{mobileMenuOpen ? "Close menu" : "Open menu"}</span>
              <svg
                className="h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth={2}
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          )}

          {/* Desktop Sidebar Toggle */}
          {showHamburgerMenu && (
            <button
              onClick={onSidebarToggle}
              className="hidden lg:flex items-center justify-center p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-all duration-200 group"
            >
              <span className="sr-only">Toggle sidebar</span>
              <svg
                className="h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          )}

          {/* Organization Switcher - Hidden on mobile when mobile menu is open */}
          <div className={mobileMenuOpen ? "hidden lg:block" : "block"}>
            <OrganizationSwitcher />
          </div>
        </div>

        {/* Center Section - Ask AI Button */}
        <div className="flex-shrink-0">
          <AskAIButton size="sm" variant="outline" className="hidden sm:flex" />
        </div>

        {/* Right Section - Header Actions */}
        <div className="flex items-center space-x-4 flex-1 justify-end">
          {headerActions}
        </div>
      </div>
    </header>
  );
}
