{"name": "@askinfosec/web-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3005", "dev:https": "next dev --turbopack --experimental-https --experimental-https-key ../../certificates/localhost-key.pem --experimental-https-cert ../../certificates/localhost.pem --port 3005", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "type-check": "tsc --noEmit", "format": "prettier --write .", "check-format": "prettier --check .", "check-lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit", "check-all": "pnpm check-format && pnpm check-lint && pnpm check-types && pnpm build"}, "dependencies": {"@askinfosec/shadcn-ui": "workspace:*", "@askinfosec/shared": "workspace:*", "@askinfosec/types": "workspace:*", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.27.16", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-table": "^8.21.3", "@tiptap/core": "3.4.4", "@tiptap/extension-color": "3.4.4", "@tiptap/extension-highlight": "^3.4.4", "@tiptap/extension-horizontal-rule": "^3.4.4", "@tiptap/extension-image": "^3.4.4", "@tiptap/extension-list": "^3.4.4", "@tiptap/extension-list-item": "3.4.4", "@tiptap/extension-subscript": "^3.4.4", "@tiptap/extension-superscript": "^3.4.4", "@tiptap/extension-text-align": "^3.4.4", "@tiptap/extension-text-style": "3.4.4", "@tiptap/extension-typography": "^3.4.4", "@tiptap/extension-underline": "^3.4.4", "@tiptap/extensions": "^3.4.4", "@tiptap/pm": "3.4.4", "@tiptap/react": "3.4.4", "@tiptap/starter-kit": "3.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.5", "lodash.throttle": "^4.1.1", "lucide-react": "^0.541.0", "motion": "^12.23.12", "nanoid": "^5.1.5", "next": "15.5.2", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-error-boundary": "^6.0.0", "react-hotkeys-hook": "^5.1.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9", "@tailwindcss/postcss": "^4.1.12", "@types/lodash.throttle": "^4.1.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "eslint": "^9", "eslint-config-next": "15.5.0", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.5", "sass": "1.89.2", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.8", "typescript": "^5"}}