{"name": "@askinfosec/shadcn-ui", "version": "0.0.1", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc && cp -r src/hooks dist/ && cp -r src/config dist/ && cp -r src/types dist/ && cp -r src/lib dist/", "dev": "tsc --watch"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.475.0", "motion": "^12.23.12", "next-themes": "^0.4.6", "nuqs": "^2.5.2", "react": "^19.1.1", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "react-hook-form": "7.57.0", "recharts": "2.15.4", "shadcn": "2.10.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "vaul": "1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@askinfosec/eslint-config": "workspace:*", "@askinfosec/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "@turbo/gen": "^2.5.5", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./components": {"import": "./dist/components/index.js", "require": "./dist/components/index.js", "types": "./dist/components/index.d.ts"}, "./components/*": {"import": "./dist/components/*.js", "require": "./dist/components/*.js", "types": "./dist/components/*.d.ts"}, "./lib": {"import": "./dist/lib/index.js", "require": "./dist/lib/index.js", "types": "./dist/lib/index.d.ts"}, "./lib/utils": {"import": "./dist/lib/utils.js", "require": "./dist/lib/utils.js", "types": "./dist/lib/utils.d.ts"}, "./lib/*": {"import": "./dist/lib/*.js", "require": "./dist/lib/*.js", "types": "./dist/lib/*.d.ts"}, "./hooks": {"import": "./dist/hooks/index.js", "require": "./dist/hooks/index.js", "types": "./dist/hooks/index.d.ts"}, "./hooks/*": {"import": "./dist/hooks/*.js", "require": "./dist/hooks/*.js", "types": "./dist/hooks/*.d.ts"}, "./config": {"import": "./dist/config/index.js", "require": "./dist/config/index.js", "types": "./dist/config/index.d.ts"}, "./config/*": {"import": "./dist/config/*.js", "require": "./dist/config/*.js", "types": "./dist/config/*.d.ts"}, "./types/*": {"import": "./dist/types/*.js", "require": "./dist/types/*.js", "types": "./dist/types/*.d.ts"}, "./postcss.config": "./postcss.config.mjs"}}