import {
  pgTable,
  varchar,
  text,
  timestamp,
  boolean,
  jsonb,
  index,
  unique,
  integer,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// User table
export const users = pgTable(
  'user',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 128 }),
    email: varchar('email', { length: 128 }),
    emailVerified: timestamp('email_verified', { withTimezone: true }),
    image: text('image'),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    defaultMfaMethod: varchar('default_mfa_method', { length: 32 }),
    mfaEnabled: boolean('mfa_enabled').default(false).notNull(),
  },
  (table) => ({
    emailIdx: index('idx_user_email').on(table.email),
    emailUnique: unique('user_email_key').on(table.email),
  })
);

// Organization table
export const organizations = pgTable(
  'organization',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    companyName: varchar('company_name', { length: 128 }).notNull(),
    mainOwnerId: varchar('main_owner_id', { length: 32 }).notNull(),
    settings: jsonb('settings'),
    openaiSettings: jsonb('openai_settings'),
    currentUsage: jsonb('current_usage'),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    otherData: jsonb('other_data'),
    companyInformation: jsonb('company_information'),
    auditTrail: jsonb('audit_trail'),
    modules: text('modules').array(),
    stripeCustomerId: text('stripe_customer_id'),
  },
  (table) => ({
    companyNameUnique: unique('organization_company_name_key').on(table.companyName),
    organizationIdx: index('idx_organization_id').on(table.id),
  })
);

// Products table
export const products = pgTable(
  'product',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: varchar('name', { length: 255 }).notNull(),
    description: text('description'),
    stripeProductId: varchar('stripe_product_id', { length: 255 }),
    isActive: boolean('is_active').default(true),
    displayOrder: integer('display_order').default(0),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  },
  (table) => ({
    nameIdx: index('idx_products_name').on(table.name),
    activeIdx: index('idx_products_active').on(table.isActive),
    stripeIdx: index('idx_products_stripe').on(table.stripeProductId),
  })
);

// System modules table
export const systemModules = pgTable(
  'module',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 64 }).notNull(),
    displayName: varchar('display_name', { length: 128 }).notNull(),
    description: varchar('description', { length: 1024 }),
    isCore: boolean('is_core').default(false),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  },
  (table) => ({
    nameIdx: index('idx_module_name').on(table.name),
    nameUnique: unique('module_name_key').on(table.name),
    coreIdx: index('idx_module_core').on(table.isCore),
  })
);

// Module dependencies table
export const moduleDependencies = pgTable(
  'module_dependency',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    moduleId: varchar('dependent_module_id', { length: 32 }).notNull().references(() => systemModules.id, { onDelete: 'cascade' }),
    dependencyId: varchar('required_module_id', { length: 32 }).notNull().references(() => systemModules.id, { onDelete: 'cascade' }),
  },
  (table) => ({
    moduleIdx: index('idx_module_dependencies_module').on(table.moduleId),
    dependencyIdx: index('idx_module_dependencies_dependency').on(table.dependencyId),
    uniqueDependency: unique('module_dependency_dependent_module_id_required_module_id_key').on(table.moduleId, table.dependencyId),
  })
);

// Product modules table
export const productModules = pgTable(
  'product_module',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    productId: varchar('product_id', { length: 32 }).notNull().references(() => products.id, { onDelete: 'cascade' }),
    moduleId: varchar('module_id', { length: 32 }).notNull().references(() => systemModules.id, { onDelete: 'cascade' }),
  },
  (table) => ({
    productIdx: index('idx_product_modules_product').on(table.productId),
    moduleIdx: index('idx_product_modules_module').on(table.moduleId),
    uniqueProductModule: unique('product_module_product_id_module_id_key').on(table.productId, table.moduleId),
  })
);

// Organization module overrides table
export const organizationModuleOverrides = pgTable(
  'organization_module_override',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    organizationId: varchar('organization_id', { length: 32 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
    moduleId: varchar('module_id', { length: 32 }).notNull().references(() => systemModules.id, { onDelete: 'cascade' }),
    isEnabled: boolean('is_enabled').default(true),
    reason: varchar('reason', { length: 1024 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  },
  (table) => ({
    organizationIdx: index('idx_org_module_overrides_org').on(table.organizationId),
    moduleIdx: index('idx_org_module_overrides_module').on(table.moduleId),
    uniqueOrgModule: unique('org_module_overrides_unique').on(table.organizationId, table.moduleId),
  })
);

// Member table (junction between User and Organization)
export const members = pgTable(
  'member',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    userId: varchar('user_id', { length: 32 }).notNull(),
    roleId: varchar('role_id', { length: 32 }).notNull(),
    departmentId: varchar('department_id', { length: 32 }),
    otherData: jsonb('other_data'),
    accessControl: jsonb('access_control'),
    auditLog: jsonb('audit_log'),
    // Enhanced RBAC fields
    status: varchar('status', { length: 32 }).default('active').notNull(),
    joinedAt: timestamp('joined_at', { withTimezone: true }).defaultNow().notNull(),
    lastActiveAt: timestamp('last_active_at', { withTimezone: true }),
    lastLoginAt: timestamp('last_login_at', { withTimezone: true }),
    twoFactorEnabled: boolean('two_factor_enabled').default(false).notNull(),
    emailVerified: boolean('email_verified').default(false).notNull(),
    jobTitle: varchar('job_title', { length: 255 }),
    phone: varchar('phone', { length: 32 }),
    timezone: varchar('timezone', { length: 64 }),
    language: varchar('language', { length: 10 }),
    notes: varchar('notes', { length: 1024 }),
    createdById: varchar('created_by_id', { length: 32 }),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }),
  },
  (table) => ({
    memberIdx: index('idx_member_id').on(table.id),
    memberStatusIdx: index('idx_member_status').on(table.status),
    memberJoinedAtIdx: index('idx_member_joined_at').on(table.joinedAt),
    uniqueMemberOrg: unique('member_organization_id_user_id_key').on(
      table.organizationId,
      table.userId
    ),
  })
);

// Member Activity table
export const memberActivities = pgTable(
  'member_activity',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    memberId: varchar('member_id', { length: 32 }).notNull(),
    type: varchar('type', { length: 64 }).notNull(),
    description: varchar('description', { length: 1024 }).notNull(),
    metadata: jsonb('metadata'),
    ipAddress: varchar('ip_address', { length: 45 }),
    userAgent: varchar('user_agent', { length: 512 }),
    location: varchar('location', { length: 255 }),
    timestamp: timestamp('timestamp', { withTimezone: true }).defaultNow().notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    memberActivityIdx: index('idx_member_activity_id').on(table.id),
    memberActivityMemberIdx: index('idx_member_activity_member_id').on(table.memberId),
    memberActivityTypeIdx: index('idx_member_activity_type').on(table.type),
    memberActivityTimestampIdx: index('idx_member_activity_timestamp').on(table.timestamp),
  })
);

// Department table
export const departments = pgTable(
  'department',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 128 }).notNull(),
    description: varchar('description', { length: 1024 }),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    createdById: varchar('created_by_id', { length: 32 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    departmentIdx: index('idx_department_id').on(table.id),
    departmentNameIdx: index('idx_department_name').on(table.name),
    departmentOrgIdx: index('idx_department_organization').on(table.organizationId),
  })
);

// Permission table
export const permissions = pgTable(
  'permission',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 128 }).notNull(),
    description: varchar('description', { length: 1024 }),
    module: varchar('module', { length: 64 }),
    category: varchar('category', { length: 32 }),
    isSystem: boolean('is_system').default(false),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    createdById: varchar('created_by_id', { length: 32 }),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    permissionIdx: index('idx_permission_id').on(table.id),
    permissionNameIdx: index('idx_permission_name').on(table.name),
    permissionModuleIdx: index('idx_permission_module').on(table.module),
  })
);

// Role Permission junction table
export const rolePermissions = pgTable(
  'role_permission',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    roleId: varchar('role_id', { length: 32 }).notNull(),
    permissionId: varchar('permission_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    createdById: varchar('created_by_id', { length: 32 }),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    rolePermissionIdx: index('idx_role_permission_id').on(table.id),
    rolePermissionRoleIdx: index('idx_role_permission_role').on(table.roleId),
    rolePermissionPermissionIdx: index('idx_role_permission_permission').on(table.permissionId),
    uniqueRolePermission: unique('role_permission_role_id_permission_id_key').on(table.roleId, table.permissionId),
  })
);

// Invite table
export const invites = pgTable(
  'invite',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    email: varchar('email', { length: 64 }),
    token: varchar('token', { length: 255 }).notNull(),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    roleId: varchar('role_id', { length: 32 }).notNull(),
    createdBy: varchar('created_by', { length: 32 }).notNull(),
    expiredAt: timestamp('expired_at', { withTimezone: true }).notNull(),
    reference: varchar('reference', { length: 255 }),
    image: varchar('image', { length: 64 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    inviteIdx: index('idx_invite_id').on(table.id),
    inviteEmailIdx: index('idx_invite_email').on(table.email),
    inviteTokenIdx: index('idx_invite_token').on(table.token),
    inviteOrgIdx: index('idx_invite_organization').on(table.organizationId),
  })
);

// Role table
export const roles = pgTable(
  'role',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 128 }).notNull(),
    isSystem: boolean('is_system').default(false).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    organizationId: varchar('organization_id', { length: 32 }),
    createdById: varchar('created_by_id', { length: 32 }),
    description: varchar('description', { length: 1024 }),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    roleIdx: index('idx_role_id').on(table.id),
    roleNameUnique: unique('role_name_key').on(table.name),
  })
);

// File table
export const files = pgTable(
  'file',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 100 }).notNull(),
    path: varchar('path', { length: 100 }),
    bufferFile: text('buffer_file'),
    checksum: text('checksum'),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    createdById: varchar('created_by_id', { length: 32 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    trainedAt: timestamp('trained_at', { withTimezone: true }),
    documentType: varchar('document_type', { length: 32 }),
    categoryId: varchar('category_id', { length: 40 }),
    scopeId: varchar('scope_id', { length: 32 }),
    accessLevel: text('access_level'),
    tags: text('tags').array(),
    fileType: text('file_type'),
    content: text('content'),
    contentChangeHistory: jsonb('content_change_history'),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
    aiSettings: jsonb('ai_settings'),
    url: text('url'),
    notes: text('notes'),
  },
  (table) => ({
    fileIdx: index('idx_file_id').on(table.id),
  })
);

// DocumentVector table
export const documentVectors = pgTable(
  'document_vector',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    content: text('content').notNull(),
    metadata: jsonb('metadata'),
    vector: text('vector'), // Unsupported("vector") in Prisma, handled as text
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    fileId: varchar('file_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    documentVectorIdx: index('idx_document_vector_id').on(table.id),
  })
);

// Attachment table
export const attachments = pgTable(
  'attachment',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    subject: varchar('subject', { length: 32 }).notNull(),
    subjectId: varchar('subject_id', { length: 32 }).notNull(),
    notes: text('notes'),
    createdById: varchar('created_by_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    fileId: varchar('file_id', { length: 32 }),
    other: jsonb('other').array(),
  },
  (table) => ({
    attachmentIdx: index('idx_attachment_id').on(table.id),
  })
);

// Evidence table
export const evidences = pgTable(
  'evidence',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    knowledgeBaseId: varchar('knowledge_base_id', { length: 32 }),
    questionId: varchar('question_id', { length: 32 }),
    fileId: varchar('file_id', { length: 32 }).notNull(),
    note: text('note'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    evidenceIdx: index('idx_evidence_id').on(table.id),
  })
);

// EvidenceTask table
export const evidenceTasks = pgTable(
  'evidence_task',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 512 }).notNull(),
    summary: text('summary'),
    guidance: text('guidance'),
    assigned: varchar('assigned', { length: 512 }).array(),
    approver: varchar('approver', { length: 512 }).array(),
    interval: varchar('interval', { length: 32 }),
    relatedEvidenceTask: varchar('related_evidence_task', { length: 512 }).array(),
    tags: varchar('tags', { length: 512 }).array(),
    otherData: jsonb('other_data'),
    createdById: varchar('created_by_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }).notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    status: varchar('status', { length: 32 }).default('draft').notNull(),
    controlId: varchar('control_id', { length: 32 }),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    evidenceTaskIdx: index('idx_evidence_task_id').on(table.id),
  })
);

// EvidenceTaskAttachment table
export const evidenceTaskAttachments = pgTable(
  'evidence_task_attachment',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    attachmentType: varchar('attachment_type', { length: 32 }).notNull(),
    note: varchar('note', { length: 1024 }),
    link: varchar('link', { length: 1024 }),
    evidenceTaskId: varchar('evidence_task_id', { length: 32 }).notNull(),
    createdById: varchar('created_by_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    fileId: varchar('file_id', { length: 32 }),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    evidenceTaskAttachmentIdx: index('idx_evidence_task_attachment_id').on(table.id),
  })
);

// Policy table
export const policies = pgTable(
  'policy',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 256 }).notNull(),
    description: text('description'),
    assigned: varchar('assigned', { length: 256 }).array(),
    approver: varchar('approver', { length: 256 }).array(),
    category: varchar('category', { length: 128 }).notNull(),
    createdById: varchar('created_by_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }).notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    status: varchar('status', { length: 32 }).default('draft').notNull(),
    content: text('content'),
    version: varchar('version', { length: 32 }),
    contentChangeHistory: jsonb('content_change_history'),
    tags: varchar('tags', { length: 512 }).array(),
    fileId: varchar('file_id', { length: 32 }),
    interval: varchar('interval', { length: 32 }),
    isProcedureDocument: boolean('is_procedure_document'),
    otherData: jsonb('other_data'),
    documentationGuide: text('documentation_guide'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    policyIdx: index('idx_policy_id').on(table.id),
  })
);

// Scope table
export const scopes = pgTable(
  'scope',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 64 }).notNull(),
    description: varchar('description', { length: 128 }).notNull(),
    scopeType: varchar('scope_type', { length: 32 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    createdById: varchar('created_by_id', { length: 32 }).notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    updatedById: varchar('updated_by_id', { length: 32 }).notNull(),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    otherData: jsonb('other_data'),
    auditLog: jsonb('audit_log'),
  },
  (table) => ({
    scopeIdx: index('idx_scope_id').on(table.id),
  })
);

// API Key table (for the current implementation)
export const apiKeys = pgTable(
  'api_key',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 64 }).notNull(),
    key: varchar('key', { length: 64 }).notNull(),
    prefix: varchar('prefix', { length: 64 }).notNull(),
    expiresAt: timestamp('expires_at', { withTimezone: true }),
    userId: varchar('user_id', { length: 32 }).notNull(),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    lastUsedAt: timestamp('last_used_at', { withTimezone: true }),
    module: varchar('module', { length: 128 }),
  },
  (table) => ({
    organizationIdx: index('api_key_organization_id_idx').on(table.organizationId),
    keyIdx: index('api_key_key_idx').on(table.key),
    prefixIdx: index('api_key_prefix_idx').on(table.prefix),
  })
);

// Email Auth Code table
export const emailAuthCodes = pgTable(
  'email_auth_code',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    userId: varchar('user_id', { length: 32 }).notNull(),
    code: varchar('code', { length: 6 }).notNull(),
    expires: timestamp('expires', { withTimezone: true }).notNull(),
    used: boolean('used').default(false).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    type: varchar('type', { length: 32 }).notNull(),
  },
  (table) => ({
    userIdIdx: index('idx_email_auth_code_userId').on(table.userId),
  })
);

// Price table
export const prices = pgTable(
  'price',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    stripePriceId: varchar('stripe_price_id', { length: 64 }),
    amount: varchar('amount', { length: 20 }), // Using varchar for decimal to match Prisma's Decimal type
    currency: varchar('currency', { length: 3 }).default('usd').notNull(),
    billingInterval: varchar('billing_interval', { length: 16 }).notNull(),
    intervalCount: integer('interval_count').default(1).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    externalPriceId: varchar('external_price_id', { length: 64 }),
  },
  (table) => ({
    stripePriceIdx: index('idx_price_stripe_price_id').on(table.stripePriceId),
    activeIdx: index('idx_price_active').on(table.isActive),
  })
);

// Subscription table
export const subscriptions = pgTable(
  'subscription',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    description: text('description').notNull(),
    productId: varchar('product_id', { length: 32 }).notNull(),
    amount: varchar('amount', { length: 20 }), // Using varchar for decimal to match Prisma's Decimal type
  },
  (table) => ({
    subscriptionIdx: index('idx_subscription_id').on(table.id),
    productIdx: index('idx_subscription_product').on(table.productId),
  })
);

// Subscription Tier table
export const subscriptionTiers = pgTable(
  'subscription_tier',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    name: varchar('name', { length: 64 }).notNull(),
    subscriptionId: varchar('subscription_id', { length: 32 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    otherData: jsonb('other_data'),
    priceId: varchar('price_id', { length: 64 }),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    subscriptionIdx: index('idx_subscription_tier_subscription').on(table.subscriptionId),
    priceIdx: index('idx_subscription_tier_price').on(table.priceId),
  })
);

// Organization Subscription table
export const organizationSubscriptions = pgTable(
  'organization_subscription',
  {
    id: varchar('id', { length: 32 }).primaryKey(),
    organizationId: varchar('organization_id', { length: 32 }).notNull(),
    subscriptionTierId: varchar('subscription_tier_id', { length: 32 }),
    stripeSubId: varchar('stripe_sub_id', { length: 64 }),
    isEnterprise: boolean('is_enterprise').default(false).notNull(),
    startDate: timestamp('start_date', { withTimezone: true }).defaultNow().notNull(),
    endDate: timestamp('end_date', { withTimezone: true }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    cancelAtPeriodEnd: boolean('cancel_at_period_end').default(false),
    canceledAt: timestamp('canceled_at', { withTimezone: true }),
    currentPeriodEnd: timestamp('current_period_end', { withTimezone: true }),
    currentPeriodStart: timestamp('current_period_start', { withTimezone: true }),
    discountId: varchar('discount_id', { length: 32 }),
    externalSubId: varchar('external_sub_id', { length: 64 }),
    status: varchar('status', { length: 32 }),
    trialEndDate: timestamp('trial_end_date', { withTimezone: true }),
  },
  (table) => ({
    organizationIdx: index('idx_organization_subscription_organization').on(table.organizationId),
    subscriptionTierIdx: index('idx_organization_subscription_tier').on(table.subscriptionTierId),
    stripeSubIdx: index('idx_organization_subscription_stripe').on(table.stripeSubId, table.externalSubId),
  })
);

// Relations
export const usersRelations = relations(users, ({ many, one }) => ({
  members: many(members),
  ownedOrganizations: many(organizations),
  files: many(files),
  apiKeys: many(apiKeys),
  emailAuthCodes: many(emailAuthCodes),
}));

export const organizationsRelations = relations(organizations, ({ many, one }) => ({
  mainOwner: one(users, {
    fields: [organizations.mainOwnerId],
    references: [users.id],
  }),
  members: many(members),
  files: many(files),
  apiKeys: many(apiKeys),
  moduleOverrides: many(organizationModuleOverrides),
  subscriptions: many(organizationSubscriptions),
}));

export const productsRelations = relations(products, ({ many }) => ({
  productModules: many(productModules),
  subscriptions: many(subscriptions),
}));

export const systemModulesRelations = relations(systemModules, ({ many }) => ({
  dependencies: many(moduleDependencies, { relationName: 'moduleDependencies' }),
  dependents: many(moduleDependencies, { relationName: 'dependentModules' }),
  productModules: many(productModules),
  organizationOverrides: many(organizationModuleOverrides),
}));

export const moduleDependenciesRelations = relations(moduleDependencies, ({ one }) => ({
  module: one(systemModules, {
    fields: [moduleDependencies.moduleId],
    references: [systemModules.id],
    relationName: 'moduleDependencies',
  }),
  dependency: one(systemModules, {
    fields: [moduleDependencies.dependencyId],
    references: [systemModules.id],
    relationName: 'dependentModules',
  }),
}));

export const productModulesRelations = relations(productModules, ({ one }) => ({
  product: one(products, {
    fields: [productModules.productId],
    references: [products.id],
  }),
  module: one(systemModules, {
    fields: [productModules.moduleId],
    references: [systemModules.id],
  }),
}));

export const organizationModuleOverridesRelations = relations(organizationModuleOverrides, ({ one }) => ({
  organization: one(organizations, {
    fields: [organizationModuleOverrides.organizationId],
    references: [organizations.id],
  }),
  module: one(systemModules, {
    fields: [organizationModuleOverrides.moduleId],
    references: [systemModules.id],
  }),
}));

export const membersRelations = relations(members, ({ one }) => ({
  user: one(users, {
    fields: [members.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [members.organizationId],
    references: [organizations.id],
  }),
  role: one(roles, {
    fields: [members.roleId],
    references: [roles.id],
  }),
  department: one(departments, {
    fields: [members.departmentId],
    references: [departments.id],
  }),
}));

export const departmentsRelations = relations(departments, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [departments.organizationId],
    references: [organizations.id],
  }),
  members: many(members),
  createdBy: one(users, {
    fields: [departments.createdById],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [departments.updatedById],
    references: [users.id],
  }),
}));

export const permissionsRelations = relations(permissions, ({ many }) => ({
  rolePermissions: many(rolePermissions),
}));

export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(roles, {
    fields: [rolePermissions.roleId],
    references: [roles.id],
  }),
  permission: one(permissions, {
    fields: [rolePermissions.permissionId],
    references: [permissions.id],
  }),
  createdBy: one(users, {
    fields: [rolePermissions.createdById],
    references: [users.id],
  }),
}));

export const invitesRelations = relations(invites, ({ one }) => ({
  organization: one(organizations, {
    fields: [invites.organizationId],
    references: [organizations.id],
  }),
  role: one(roles, {
    fields: [invites.roleId],
    references: [roles.id],
  }),
  createdBy: one(users, {
    fields: [invites.createdBy],
    references: [users.id],
  }),
}));

export const rolesRelations = relations(roles, ({ many, one }) => ({
  members: many(members),
  createdBy: one(users, {
    fields: [roles.createdById],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [roles.updatedById],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [roles.organizationId],
    references: [organizations.id],
  }),
}));

export const filesRelations = relations(files, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [files.organizationId],
    references: [organizations.id],
  }),
  createdBy: one(users, {
    fields: [files.createdById],
    references: [users.id],
  }),
  scope: one(scopes, {
    fields: [files.scopeId],
    references: [scopes.id],
  }),
  attachments: many(attachments),
  documentVectors: many(documentVectors),
  evidences: many(evidences),
  evidenceTaskAttachments: many(evidenceTaskAttachments),
  policies: many(policies),
}));

export const documentVectorsRelations = relations(documentVectors, ({ one }) => ({
  file: one(files, {
    fields: [documentVectors.fileId],
    references: [files.id],
  }),
  organization: one(organizations, {
    fields: [documentVectors.organizationId],
    references: [organizations.id],
  }),
}));

export const attachmentsRelations = relations(attachments, ({ one }) => ({
  file: one(files, {
    fields: [attachments.fileId],
    references: [files.id],
  }),
  createdBy: one(users, {
    fields: [attachments.createdById],
    references: [users.id],
  }),
}));

export const evidencesRelations = relations(evidences, ({ one }) => ({
  file: one(files, {
    fields: [evidences.fileId],
    references: [files.id],
  }),
}));

export const evidenceTasksRelations = relations(evidenceTasks, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [evidenceTasks.organizationId],
    references: [organizations.id],
  }),
  createdBy: one(users, {
    fields: [evidenceTasks.createdById],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [evidenceTasks.updatedById],
    references: [users.id],
  }),
  evidenceTaskAttachments: many(evidenceTaskAttachments),
}));

export const evidenceTaskAttachmentsRelations = relations(evidenceTaskAttachments, ({ one }) => ({
  evidenceTask: one(evidenceTasks, {
    fields: [evidenceTaskAttachments.evidenceTaskId],
    references: [evidenceTasks.id],
  }),
  file: one(files, {
    fields: [evidenceTaskAttachments.fileId],
    references: [files.id],
  }),
  createdBy: one(users, {
    fields: [evidenceTaskAttachments.createdById],
    references: [users.id],
  }),
}));

export const policiesRelations = relations(policies, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [policies.organizationId],
    references: [organizations.id],
  }),
  file: one(files, {
    fields: [policies.fileId],
    references: [files.id],
  }),
  createdBy: one(users, {
    fields: [policies.createdById],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [policies.updatedById],
    references: [users.id],
  }),
}));

export const scopesRelations = relations(scopes, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [scopes.organizationId],
    references: [organizations.id],
  }),
  createdBy: one(users, {
    fields: [scopes.createdById],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [scopes.updatedById],
    references: [users.id],
  }),
  files: many(files),
}));

export const apiKeysRelations = relations(apiKeys, ({ one }) => ({
  user: one(users, {
    fields: [apiKeys.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [apiKeys.organizationId],
    references: [organizations.id],
  }),
}));

export const emailAuthCodesRelations = relations(emailAuthCodes, ({ one }) => ({
  user: one(users, {
    fields: [emailAuthCodes.userId],
    references: [users.id],
  }),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one, many }) => ({
  product: one(products, {
    fields: [subscriptions.productId],
    references: [products.id],
  }),
  subscriptionTiers: many(subscriptionTiers),
}));

export const pricesRelations = relations(prices, ({ many }) => ({
  subscriptionTiers: many(subscriptionTiers),
}));

export const subscriptionTiersRelations = relations(subscriptionTiers, ({ one, many }) => ({
  subscription: one(subscriptions, {
    fields: [subscriptionTiers.subscriptionId],
    references: [subscriptions.id],
  }),
  price: one(prices, {
    fields: [subscriptionTiers.priceId],
    references: [prices.id],
  }),
  organizationSubscriptions: many(organizationSubscriptions),
}));

export const organizationSubscriptionsRelations = relations(organizationSubscriptions, ({ one }) => ({
  organization: one(organizations, {
    fields: [organizationSubscriptions.organizationId],
    references: [organizations.id],
  }),
  subscriptionTier: one(subscriptionTiers, {
    fields: [organizationSubscriptions.subscriptionTierId],
    references: [subscriptionTiers.id],
  }),
}));

// Export schema for Drizzle
export const schema = {
  users,
  organizations,
  products,
  systemModules,
  moduleDependencies,
  productModules,
  organizationModuleOverrides,
  members,
  memberActivities,
  departments,
  permissions,
  rolePermissions,
  invites,
  roles,
  files,
  documentVectors,
  attachments,
  evidences,
  evidenceTasks,
  evidenceTaskAttachments,
  policies,
  scopes,
  apiKeys,
  emailAuthCodes,
  prices,
  subscriptions,
  subscriptionTiers,
  organizationSubscriptions,
  usersRelations,
  organizationsRelations,
  productsRelations,
  systemModulesRelations,
  moduleDependenciesRelations,
  productModulesRelations,
  organizationModuleOverridesRelations,
  membersRelations,
  departmentsRelations,
  permissionsRelations,
  rolePermissionsRelations,
  invitesRelations,
  rolesRelations,
  filesRelations,
  documentVectorsRelations,
  attachmentsRelations,
  evidencesRelations,
  evidenceTasksRelations,
  evidenceTaskAttachmentsRelations,
  policiesRelations,
  scopesRelations,
  apiKeysRelations,
  emailAuthCodesRelations,
  pricesRelations,
  subscriptionsRelations,
  subscriptionTiersRelations,
  organizationSubscriptionsRelations,
};

// Export types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Organization = typeof organizations.$inferSelect;
export type NewOrganization = typeof organizations.$inferInsert;

export type Product = typeof products.$inferSelect;
export type NewProduct = typeof products.$inferInsert;

export type SystemModule = typeof systemModules.$inferSelect;
export type NewSystemModule = typeof systemModules.$inferInsert;

export type ModuleDependency = typeof moduleDependencies.$inferSelect;
export type NewModuleDependency = typeof moduleDependencies.$inferInsert;

export type ProductModule = typeof productModules.$inferSelect;
export type NewProductModule = typeof productModules.$inferInsert;

export type OrganizationModuleOverride = typeof organizationModuleOverrides.$inferSelect;
export type NewOrganizationModuleOverride = typeof organizationModuleOverrides.$inferInsert;

export type Member = typeof members.$inferSelect;
export type NewMember = typeof members.$inferInsert;

export type MemberActivity = typeof memberActivities.$inferSelect;
export type NewMemberActivity = typeof memberActivities.$inferInsert;

export type Department = typeof departments.$inferSelect;
export type NewDepartment = typeof departments.$inferInsert;

export type Permission = typeof permissions.$inferSelect;
export type NewPermission = typeof permissions.$inferInsert;

export type RolePermission = typeof rolePermissions.$inferSelect;
export type NewRolePermission = typeof rolePermissions.$inferInsert;

export type Invite = typeof invites.$inferSelect;
export type NewInvite = typeof invites.$inferInsert;

export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;

export type File = Omit<typeof files.$inferSelect, 'bufferFile'> & {
  bufferFile: string | null; // Base64 encoded string
};
export type NewFile = Omit<typeof files.$inferInsert, 'bufferFile'> & {
  bufferFile?: string; // Base64 encoded string
};

export type DocumentVector = typeof documentVectors.$inferSelect;
export type NewDocumentVector = typeof documentVectors.$inferInsert;

export type Attachment = typeof attachments.$inferSelect;
export type NewAttachment = typeof attachments.$inferInsert;

export type Evidence = typeof evidences.$inferSelect;
export type NewEvidence = typeof evidences.$inferInsert;

export type EvidenceTask = typeof evidenceTasks.$inferSelect;
export type NewEvidenceTask = typeof evidenceTasks.$inferInsert;

export type EvidenceTaskAttachment = typeof evidenceTaskAttachments.$inferSelect;
export type NewEvidenceTaskAttachment = typeof evidenceTaskAttachments.$inferInsert;

export type Policy = typeof policies.$inferSelect;
export type NewPolicy = typeof policies.$inferInsert;

export type Scope = typeof scopes.$inferSelect;
export type NewScope = typeof scopes.$inferInsert;

export type ApiKey = typeof apiKeys.$inferSelect;
export type NewApiKey = typeof apiKeys.$inferInsert;

export type EmailAuthCode = typeof emailAuthCodes.$inferSelect;
export type NewEmailAuthCode = typeof emailAuthCodes.$inferInsert;

export type Price = typeof prices.$inferSelect;
export type NewPrice = typeof prices.$inferInsert;

export type Subscription = typeof subscriptions.$inferSelect;
export type NewSubscription = typeof subscriptions.$inferInsert;

export type SubscriptionTier = typeof subscriptionTiers.$inferSelect;
export type NewSubscriptionTier = typeof subscriptionTiers.$inferInsert;

export type OrganizationSubscription = typeof organizationSubscriptions.$inferSelect;
export type NewOrganizationSubscription = typeof organizationSubscriptions.$inferInsert;
