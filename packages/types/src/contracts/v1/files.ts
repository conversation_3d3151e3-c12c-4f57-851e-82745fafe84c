import { z } from "zod";

// Transport enums
export const FileAccessLevelSchema = z.enum([
  "public",
  "internal",
  "confidential",
  "restricted"
]);

export const DocumentTypeSchema = z.enum([
  "policy",
  "procedure",
  "control",
  "document",
  "image",
  "video",
  "audio",
  "other"
]);

// File Schema
export const FileSchema = z.object({
  id: z.string(),
  name: z.string(),
  path: z.string().optional(),
  bufferFile: z.string().optional(), // Base64 encoded binary data from buffer_file field
  checksum: z.string().optional(),
  organizationId: z.string(),
  createdById: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  trainedAt: z.string().optional(),
  documentType: DocumentTypeSchema.optional(),
  categoryId: z.string().optional(),
  scopeId: z.string().optional(),
  accessLevel: FileAccessLevelSchema.optional(),
  tags: z.array(z.string()).optional().default([]),
  fileType: z.string().optional(),
  content: z.string().optional(), // If content is edited directly using rich text editor, this will be the content of the file
  url: z.string().optional(),
  notes: z.string().optional(),
  // Relations
  createdBy: z.object({
    id: z.string(),
    name: z.string().optional(),
    email: z.string().optional(),
    image: z.string().optional(),
  }).optional(),
  scope: z.object({
    id: z.string(),
    name: z.string(),
  }).optional(),
});

// File Stats Schema
export const FileStatsSchema = z.object({
  total: z.number().optional().default(0),
  byDocumentType: z.record(z.string(), z.number()).optional().default({}),
  byAccessLevel: z.record(z.string(), z.number()).optional().default({}),
  trained: z.number().optional().default(0),
  untrained: z.number().optional().default(0),
  recentUploads: z.number().optional().default(0),
});

// Request/Response DTOs
export const GetFilesRequestSchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(20),
  sortBy: z.string().optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
  filters: z.object({
    search: z.string().optional(),
    documentType: DocumentTypeSchema.array().optional(),
    accessLevel: FileAccessLevelSchema.array().optional(),
    categoryId: z.string().array().optional(),
    scopeId: z.string().array().optional(),
    fileType: z.string().array().optional(),
    createdById: z.string().array().optional(),
    includeTrained: z.boolean().optional(),
    excludeTrained: z.boolean().optional(),
    createdAfter: z.string().optional(),
    createdBefore: z.string().optional(),
  }).optional(),
});

export const GetFilesResponseSchema = z.object({
  files: z.array(FileSchema).optional().default([]),
  stats: FileStatsSchema.optional().default(FileStatsSchema.parse({})),
  total: z.number().optional().default(0),
  page: z.number().optional().default(1),
  limit: z.number().optional().default(20),
  hasMore: z.boolean().optional().default(false),
});

// Types
export type GetFilesRequest = z.infer<typeof GetFilesRequestSchema>;
export type GetFilesResponse = z.infer<typeof GetFilesResponseSchema>;
export type FileDTO = z.infer<typeof FileSchema>;
export type FileStatsDTO = z.infer<typeof FileStatsSchema>;
export type FileAccessLevel = z.infer<typeof FileAccessLevelSchema>;
export type DocumentType = z.infer<typeof DocumentTypeSchema>;